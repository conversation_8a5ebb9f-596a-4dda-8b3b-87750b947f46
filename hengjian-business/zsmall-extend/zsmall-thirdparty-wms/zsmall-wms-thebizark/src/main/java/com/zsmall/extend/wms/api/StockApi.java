package com.zsmall.extend.wms.api;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.zsmall.extend.core.MallHttpResponse;
import com.zsmall.extend.core.kit.HttpKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.base.Result;
import com.zsmall.extend.wms.model.stock.OutStock;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存接口
 */
@Slf4j
public class StockApi extends Component {

    /**
     * 查询库存
     */
    //private final String STOCK_URL_GET_INVENTORY = "/inventory/getInventory";
    private final String STOCK_URL_GET_INVENTORY = "/inventory/getInventoryZone";
    //最新获取库存地址
    private final String STOCK_URL_GET_INVENTORY_20240511 = "/inventory/inventoryOfWarehouse";
    private final String STOCK_URL_GET_INVENTORY_20250905 = "/inventory/inventoryOfWarehouse/latest";


    public StockApi(ThebizarkBean thebizarkBean) {
        super(thebizarkBean);
    }


    /**
     * 查询库存
     *
     * @param skus
     * @return
     */
    public Result<List<OutStock>> getInventory(List<String> skus) {
        Result<List<OutStock>> result = new Result<>();
        Map<String, Object> params = new HashMap<>(1);
        params.put("skus", skus);

        Result<List<OutStock>> listResult = null;
        try {
            MallHttpResponse mallHttpResponse = HttpKit.getDelegate().post(getRequestUrl(STOCK_URL_GET_INVENTORY_20250905), JSONUtil.toJsonStr(params), getAuthorizationHeader());

            checkResultStatus(mallHttpResponse);

            listResult = mallHttpResponse
                .toBean(new TypeReference<Result<List<OutStock>>>() {
                });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return listResult;
    }

}
