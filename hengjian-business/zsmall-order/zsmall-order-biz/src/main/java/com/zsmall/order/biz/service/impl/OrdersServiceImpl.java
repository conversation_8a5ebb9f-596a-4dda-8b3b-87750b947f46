package com.zsmall.order.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.utils.file.FileUtils;
import com.hengjian.common.excel.convert.ExcelBigNumberConvert;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.service.ISysApiService;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.domain.SysUser;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.mapper.SysTenantMapper;
import com.hengjian.system.mapper.SysUserMapper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.domain.tiktok.domain.dto.address.SiteMsgBo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.domain.vo.IntactAddressInfoVo;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.*;
import com.zsmall.common.util.ImagesUtil;
import com.zsmall.common.util.PDFUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.base.Result;
import com.zsmall.extend.wms.model.order.CancelOutOrder;
import com.zsmall.extend.wms.model.order.OutShipment;
import com.zsmall.lottery.support.PriceBussinessV2Support;
import com.zsmall.order.biz.factory.ListenerFactory;
import com.zsmall.order.biz.factory.OrderHandleInterface;
import com.zsmall.order.biz.listener.OrderItemTrackingListener;
import com.zsmall.order.biz.mq.CanalOrderSupper;
import com.zsmall.order.biz.service.IProductAboutManger;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.anno.annotaion.PayLimit;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import com.zsmall.order.entity.domain.bo.order.*;
import com.zsmall.order.entity.domain.bo.refund.RefundApplyHandleBo;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentCallBackDTO;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderDTO;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderItemDTO;
import com.zsmall.order.entity.domain.mq.EsOrdersDTO;
import com.zsmall.order.entity.domain.openapi.OpenApiOrdersDeliveryDTO;
import com.zsmall.order.entity.domain.vo.OrderListVo;
import com.zsmall.order.entity.domain.vo.order.*;
import com.zsmall.order.entity.domain.vo.tracking.OrderItemTrackingVO;
import com.zsmall.order.entity.esmapper.EsOrderMapper;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.manger.IOrderManger;
import com.zsmall.order.entity.mapper.OrderItemProductSkuMapper;
import com.zsmall.order.entity.mapper.OrdersMapper;
import com.zsmall.order.entity.mapper.TemporaryTableMapper;
import com.zsmall.order.entity.util.TimeUtil;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.event.CheckPaymentPasswordEvent;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.domain.vo.salesChannel.TrackingCallBackPackagesItemsVo;
import com.zsmall.system.entity.domain.vo.salesChannel.TrackingCallBackPackagesVo;
import com.zsmall.system.entity.domain.vo.salesChannel.TrackingCallBackVo;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.easyes.core.biz.SAPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.channels.FileChannel;
import java.nio.file.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 主订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrdersServiceImpl implements OrdersService {
    private static final DynamicRoutingDataSource DS = SpringUtils.getBean(DynamicRoutingDataSource.class);
    private final OrderRefundServiceImpl orderRefundServiceImpl;

    private final ISysApiService iSysApiService;

    private final IProductMappingService iProductMappingService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final OrderSupport orderSupport;
    private final IProductSkuPriceService productSkuPriceService;
    private final IProductAboutManger productAboutManger;
    private final ISysTenantService sysTenantService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final OrdersMapper ordersMapper;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final SysUserMapper sysUserMapper;
    private final TemporaryTableMapper temporaryTableMapper;
    private final PriceBussinessV2Support priceBussinessSupport;
    private final IOrderItemService orderItemService;
    private final IProductSkuService productSkuService;
    private final IOrderItemProductSkuService orderItemProductSkuService;
    private final IOrderItemTrackingRecordService orderItemTrackingRecordService;
    private final IWarehouseService warehouseService;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemPriceService orderItemPriceService;
    private final IOrderLogisticsInfoService orderLogisticsInfoService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final ITenantSalesChannelService tenantSalesChannelService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final ProductSkuStockService productSkuStockService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderRefundService iOrderRefundService;
    private final IWarehouseBizArkConfigService iWarehouseBizArkConfigService ;
    private final IWarehouseService iWarehouseService;
    private static final ThreadPoolExecutor ioThreadPoolExecutor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);

    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        if (ObjectUtil.isEmpty(bizArkConfig)) {
            log.error("未找到供应商{}的配置信息", supplierId);
            return null;
        }
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }

    @Value("${distribution.export.limit}")
    private Integer exportLimit;
    @Value("${orderAttachment.ossRootPath}")
    public String ossRootPath;
    private static final ThreadPoolTaskExecutor threadPoolTaskExecutor = SpringUtils.getBean("threadPoolTaskExecutor", ThreadPoolTaskExecutor.class);
    private final IOrdersService iOrdersService;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    private final List<OrderHandleInterface> orderHandleInterfaces;
    private final PushToThirdUtils pushToThirdUtils;
    private final SysTenantMapper sysTenantMapper;
    private final OrderItemProductSkuMapper orderItemProductSkuMapper;
    private final RabbitTemplate rabbitTemplate;
    @Resource
    private IOrderManger iOrderManger;
    private final IOrdersService ordersService;
    @Resource
    private ListenerFactory listenerFactory ;

    @Resource
    private EsOrderMapper esOrderMapper;
    @Resource
    private CanalOrderSupper canalOrderSupper;
    /**
     * 查询主订单
     */
    @Override
    public OrderDetailVo queryByOrderNo(String orderNo) {
        Orders orders = iOrdersService.getByOrderNo(orderNo);

        Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                   filter(oif -> oif.isThisImpl(orders.getOrderType()))
                                                                                   .findFirst();

        OrderDetailVo orderDetailVo;
        if (orderHandleInterface.isPresent()) {
            orderDetailVo = orderHandleInterface.get().buildOrderDetail(orders);
        } else {
            orderDetailVo = orderHandleInterfaces.stream().
                                                 filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst().get()
                                                 .buildOrderDetail(orders);
        }
        return orderDetailVo;
    }

    /**
     * 查询主订单列表
     */
    @Override
    public TableDataInfo<OrderListVo> queryPageList(OrdersPageBo dto, PageQuery pageQuery) {
        TimeInterval timer = DateUtil.timer();
        timer.start("1");
        log.info("调用【查询订单列表】接口，查询订单列表接口请求参数：{} ", JSONUtil.toJsonStr(dto));
        dto = getOrdersPageBo(dto);
        Integer limit = pageQuery.getPageSize();
        Integer offset = (pageQuery.getPageNum() - 1) * pageQuery.getPageSize();
        Console.log("[订单列表查询耗时]前端参数转换: {} ms", timer.intervalMs("1"));
        try {
            timer.start("2");
            OrdersPageBo finalDto = dto;
            Integer count =   TenantHelper.ignore(()->iOrdersService.countByOrderListVo(finalDto));
            Console.log("[订单列表查询耗时]数据库查询总数: {} ms", timer.intervalMs("2"));
            timer.start("3");
            List<OrderListVo> orderPage =  iOrdersService.queryOrderListVoPageList(dto, offset,limit);
            Console.log("[订单列表查询耗时]数据库查询数据: {} ms", timer.intervalMs("3"));
            List<OrderListVo> orderListVoList= dealOrderListVo(orderPage);
            return TableDataInfo.build(orderListVoList, count);
        } catch (Exception e) {
            log.error("错误信息:",e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 订单查询条件构建,适用于订单列表查询/订单导出/附件导出/展示订单导出
     * @param dto
     * @return
     */
    public OrdersPageBo getOrdersPageBo(OrdersPageBo dto){
        List<String>orderNos;
        List<String>channelOrderNames;
        if (StrUtil.isNotBlank(dto.getQueryType()) && StrUtil.isNotBlank(dto.getQueryValue())) {
            String value = dto.getQueryValue();
            switch (dto.getQueryType()) {
                case "OrderNo":
                    // 逗号
                    String[] orderArray = value.split(",");
                    // 将结果存储在 List<String> 中
                    orderNos = new ArrayList<>(Arrays.asList(orderArray));
                    if(CollUtil.isNotEmpty(orderNos)&&orderNos.size()>1){
                        dto.putOrderNos(orderNos);
                    }else {
                        dto.setOrderNo(value);
                    }
                    break;
                case "ChannelOrderName":
                    String[] channelArray = value.split(",");
                    // 将结果存储在 List<String> 中
                    channelOrderNames = new ArrayList<>(Arrays.asList(channelArray));
                    if(CollUtil.isNotEmpty(channelOrderNames)&&channelOrderNames.size()>1){
                        dto.putChannelOrderNos(channelOrderNames);
                    }else {
                        dto.setChannelOrderNo(value);
                    }
                    break;
                case "Sku":
                    dto.setSku(value);
                    break;
                case "ProductName":
                    dto.setProductName(value);
                    break;
                case "ProductSkuCode":
                    dto.setItemNo(value);
                    break;
                case "TrackingNo":
                    dto.setTrackingNo(value);
                    break;
                case "ActivityID":
                    dto.setActivityCode(value);
                    break;
                case "SupplierTenantId":
                    dto.setSupplierTenantId(value);
                    break;
                case "DistributorTenantId":
                    dto.setDistributorTenantId(value);
                    break;
                default:
                    break;
            }
        }
        String orderState = dto.getOrderState();
        //订单状态
        List<String> orderStateTypes = new ArrayList<>();
        String fulfillmentType = "";
        if (StrUtil.isNotBlank(orderState)) {
            if (StrUtil.equals(orderState, OrderStateType.UnPaid.name())) {
                orderStateTypes.add(OrderStateType.UnPaid.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Failed.name())) {
                orderStateTypes.add(OrderStateType.Failed.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Refunded.name())) {
                orderStateTypes.add(OrderStateType.Refunded.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.UnDispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                orderStateTypes.add(OrderStateType.Verifying.name());
                fulfillmentType = LogisticsProgress.UnDispatched.name();
            } else if (StrUtil.equals(orderState, LogisticsProgress.Dispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.Dispatched.name();
            } else if (StrUtil.equals(orderState, OrderStateType.Canceled.name())) {
                orderStateTypes.add(OrderStateType.Canceled.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.Fulfilled.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.Fulfilled.name();
            } else if (StrUtil.equals(orderState, OrderStateType.Pending.name())) {
                orderStateTypes.add(OrderStateType.Pending.name());
            }
        }
        String sortValue = dto.getSortValue();
        if (!dto.getIsOrderEsSearch()){
            switch (sortValue) {
                case "createTimeAsc":
                    sortValue = "o.create_time asc";
                    break;
                case "createTimeDesc":
                    sortValue = "o.create_time desc";
                    break;

                case "orderNoAsc":
                    sortValue = "o.id asc";
                    break;
                case "orderNoDesc":
                    sortValue = "o.id desc";
                    break;
                case "priceAsc":
                    sortValue = LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)
                        ? "o.platform_payable_total_amount asc"
                        : "o.original_payable_total_amount asc";
                    break;
                case "priceDesc":
                    sortValue = LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)
                        ? "o.platform_payable_total_amount desc"
                        : "o.original_payable_total_amount desc";
                    break;
                case "latestDeliveryTimeDesc":
                    sortValue = "o.latest_delivery_time desc";
                    break;
                case "latestDeliveryTimeAsc":
                    sortValue = "CASE WHEN o.latest_delivery_time IS NULL THEN 1 ELSE 0 END,o.latest_delivery_time ASC";

                    break;
                default:
                    sortValue="o.id desc";
                    break;

            }
            dto.setSortValue(sortValue);
        }
        dto.setOrderStates(orderStateTypes);
        dto.setFulfillmentType(fulfillmentType);
        dto.setTenantType(LoginHelper.getTenantType());
        dto.setTenantId(LoginHelper.getTenantId());
        //分销商
        if (ObjectUtil.equals(LoginHelper.getTenantType(), TenantType.Distributor.name())) {
            dto.setDistributorTenantId(LoginHelper.getTenantId());
        }
        //供应商
        if (ObjectUtil.equals(LoginHelper.getTenantType(), TenantType.Supplier.name())) {
            dto.setSupplierTenantId(LoginHelper.getTenantId());
        }
        //管理员
        if (ObjectUtil.equals(LoginHelper.getTenantType(), TenantType.Manager.name())) {
            if (StrUtil.isNotEmpty(dto.getSupplierTenantId())){
                dto.setSupplierTenantId(dto.getSupplierTenantId());
            }
            if (StrUtil.isNotEmpty(dto.getDistributorTenantId())){
                dto.setDistributorTenantId(dto.getDistributorTenantId());
            }
        }
        return dto;
    }

    public static <T> List<List<T>> partitionList(List<T> list, int size) {
        if (list == null || size <= 0) {
            throw new IllegalArgumentException("Invalid partition size");
        }

        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(new ArrayList<>(list.subList(i, Math.min(i + size, list.size()))));
        }
        return partitions;
    }

    /**
     * @description:  订单列表查询结果处理
     * @author: len
    *  @date: 2024/8/14 18:18
     * @param: orderListVos
     * @return: java.util.List<com.zsmall.order.entity.domain.vo.OrderListVo>
     **/
    private List<OrderListVo> dealOrderListVo(List<OrderListVo> orderListVos) {
        Set<Long> channelIdSet = orderListVos.stream().map(OrderListVo::getChannelId).filter(channelId -> channelId != null && ObjectUtil.isNotEmpty(channelId))
                .collect(Collectors.toSet());
        //查询所有的渠道店铺名
        LambdaQueryWrapper<TenantSalesChannel> q = new LambdaQueryWrapper<>();
        q.in(CollectionUtil.isNotEmpty(channelIdSet),TenantSalesChannel::getId,channelIdSet);
        List<TenantSalesChannel> tenantSalesChannels =TenantHelper.ignore(()-> iTenantSalesChannelService.getBaseMapper().selectList(q));
        Map<Long, TenantSalesChannel> tenantSalesChannelMap = tenantSalesChannels.stream().collect(
                Collectors.toMap(TenantSalesChannel::getId, Function.identity(), (existing, replacement) -> existing));
        Set<Long> createBySet = orderListVos.stream().map(OrderListVo::getCreateBy).filter(createBy -> createBy != null && ObjectUtil.isNotEmpty(createBy))
                .collect(Collectors.toSet());
        LambdaQueryWrapper<SysUser> qq = new LambdaQueryWrapper<>();
        qq.in(CollectionUtil.isNotEmpty(createBySet),SysUser::getUserId,createBySet);
        List<SysUserVo> sysUserList = TenantHelper.ignore(() -> sysUserMapper.selectUserList(qq));
        Map<Long, SysUserVo> userMap = sysUserList.stream()
                .collect(Collectors.toMap(SysUserVo::getUserId, sysUser -> sysUser));
        //查询商品信息
        List<String> collect = orderListVos.stream()
                                           .map(OrderListVo::getOrderId)
                                           .collect(Collectors.toList());
        // 集合orderListVos内 exceptionCode为10的为异常订单,过滤出这批订单的订单编号
        //查询商品信息
        List<String> exceptionOrderSns = orderListVos.stream()
                                                     .filter(ObjectUtil::isNotEmpty)
                                                     .filter(order ->
                                                         OrderExceptionEnum.abnormal_exception.getValue()
                                                                                              .equals(order.getExceptionCode()))
                                                     .map(OrderListVo::getOrderId)
                                                     .collect(Collectors.toList());
        Map<String, JSONObject> map = null;
        if(CollUtil.isNotEmpty(exceptionOrderSns)){
            map = iOrderItemShippingRecordService.queryMapByOrderNoList(exceptionOrderSns);
        }

        Map<String, OrderItemProductSku> orderItemProductSkuHashMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(collect)) {
            LambdaQueryWrapper<OrderItemProductSku> qq1 = new LambdaQueryWrapper<>();
            qq1.in(OrderItemProductSku::getOrderNo,collect);
            List<OrderItemProductSku> productSkus =  TenantHelper.ignore(()->orderItemProductSkuMapper.selectList(qq1));
            orderItemProductSkuHashMap = productSkus.stream().collect(Collectors.toMap(OrderItemProductSku::getOrderNo, s -> s));
        }
        //筛选出所有的渠道订单号
        for (OrderListVo s : orderListVos) {
            String orderId = s.getOrderId();
            if(CollUtil.isNotEmpty(map)&&map.containsKey(orderId)){
                s.setAbnormalMessage(map.get(orderId));
            }
            //处理销售渠道
            if (ObjectUtil.isNotNull(s.getChannelId())){
                TenantSalesChannel tenantSalesChannel = tenantSalesChannelMap.get(s.getChannelId());
                if (ObjectUtil.isNotNull(tenantSalesChannel)){
                    s.setSalesChannel(tenantSalesChannel.getChannelType());
                }else {
                    s.setSalesChannel(s.getChannelType());
                }
            }else {
                s.setSalesChannel(s.getChannelType());
            }
            //处理订单状态
            if (ObjectUtil.isNotNull(s.getOrderState())) {
                List<String> orderStatusTypes = new ArrayList<>();
                orderStatusTypes.add(OrderStateType.UnPaid.name());
                orderStatusTypes.add(OrderStateType.Failed.name());
                orderStatusTypes.add(OrderStateType.Canceled.name());
                orderStatusTypes.add(OrderStateType.Refunded.name());
                orderStatusTypes.add(OrderStateType.Pending.name());
                if (orderStatusTypes.contains(s.getOrderState())) {
                    s.setOrderStatus(s.getOrderState());
                }else {
                    s.setOrderStatus(s.getFulfillment());
                }
            }
            String tenantType = LoginHelper.getTenantType();
            OrderListVo.OrderItems orderItems = s.getOrderItems().get(0);
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal productTotalPrice = BigDecimal.ZERO;
            //处理金额
            if (ObjectUtil.equals(tenantType, TenantType.Supplier.name())) {
                productTotalPrice=s.getOriginalActualTotalAmount();
                totalAmount=s.getOriginalActualTotalAmount();
            }else {
                if(OrderSourceEnum.INTERFACE_ORDER.getValue().equals(s.getOrderSource())||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(s.getOrderSource())){
                    if(LogisticsTypeEnum.DropShipping.name().equals(s.getLogisticsType())){
                        productTotalPrice = s.getOriginalTotalDropShippingPrice();
                    }
                    if(LogisticsTypeEnum.PickUp.name().equals(s.getLogisticsType())){
                        productTotalPrice = s.getOriginalTotalPickUpPrice();
                    }
                }else{
                    productTotalPrice=s.getPlatformActualTotalAmount();
                }

                if(LogisticsTypeEnum.DropShipping.name().equals(s.getLogisticsType())){
                    totalAmount = s.getOriginalTotalDropShippingPrice();
                }
                if(LogisticsTypeEnum.PickUp.name().equals(s.getLogisticsType())){
                    totalAmount = s.getOriginalTotalPickUpPrice();
                }
            }
            SysUserVo sysUser = userMap.get(s.getCreateBy());
            if (ObjectUtil.isNotNull(sysUser)){
                s.setDistributor( DesensitizedUtil.idCardNum(sysUser.getUserName(), 1, 1));
            }
            //处理超管分销商/供应商信息
            if (!ObjectUtil.equals(TenantType.Manager.name(), tenantType)) {
                s.setDistributorId(null);
                s.setSupplierIds(null);
                s.setDistributor(null);
            }
            s.setTotal(totalAmount);
            orderItems.setProductTotalPrice(productTotalPrice);
//            s.setTotal(ObjectUtil.isNotNull(totalAmount)?totalAmount:BigDecimal.ZERO);
//            orderItems.setProductTotalPrice(ObjectUtil.isNotNull(productTotalPrice)?productTotalPrice:BigDecimal.ZERO);
            String orderItemStatus = orderItems.getOrderState();
            String fulfillment = orderItems.getFulfillmentProgress();
            //子订单确认收货按钮
            boolean canConfirmReceipt = ObjectUtil.equals(orderItemStatus, OrderStateType.Paid) && ObjectUtil.equals(fulfillment, LogisticsProgress.Dispatched);
            orderItems.setCanConfirmReceipt(canConfirmReceipt);
            //处理商品信息
            OrderListVo.OrderItems items = s.getOrderItems().get(0);
            OrderItemProductSku orderItemProductSku = orderItemProductSkuHashMap.get(orderId);
            if (ObjectUtil.isNotNull(orderItemProductSku)) {
                items.setSku(orderItemProductSku.getSku());
                items.setProductName(orderItemProductSku.getProductName());
                items.setImageShowUrl(orderItemProductSku.getImageShowUrl());
            }
        }
        return orderListVos;
    }

    /**
     * 支付订单
     *
     * @param bo
     * @return
     */
    @Override
    @PayLimit(timeUnit = TimeUnit.SECONDS,interval = 4)
    public R<Void> payOrder(OrderPayBo bo) throws Exception {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        String tenantId = LoginHelper.getTenantId();
        //      测试使用
//        String tenantId = "D62PA23";
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(bo.getPaymentPassword()));
        List<Orders> orderList = iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed);
        payOrderBeforeCheck(orderList, tenantId);
        if (CollUtil.size(orderNoList) == CollUtil.size(orderNoList)) {
            orderSupport.orderPayChain(tenantId, orderList, true, true);
        } else {
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }
        return R.ok(ZSMallStatusCodeEnum.PAY_ORDER_SUCCESS);
    }

    /**
     * 支付订单
     *
     * @param bo
     * @return
     */
    @Override
//    @PayLimit(interval = 4)
    @InMethodLog("订单支付队列方法")
    public R<Void> payOrderQueue(OrderPayBo bo) throws OrderPayException {
        String tenantId = LoginHelper.getTenantId();
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        // 校验支付密码
        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(bo.getPaymentPassword()));
        List<Orders> orderList = iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed);
        if (CollUtil.size(orderNoList) != CollUtil.size(orderList)) {
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }
        if(CollUtil.isEmpty(orderList)){
            return R.fail(ZSMallStatusCodeEnum.ORDERS_IS_NOT_EXIST);
        }
        // 校验订单的异常状态
        orderSupport.orderExceptionStatusCheck(orderList);
        // 附件检查
        payOrderBeforeCheck(orderList, tenantId);
        // 修改订单为支付中
        List<Orders> orderPendingList = orderSupport.orderLockToPendingUpdate(tenantId, orderList);
        // 判断订单状态是否变更，发送队列
        if (CollUtil.size(orderNoList) == CollUtil.size(orderPendingList)) {
            for(Orders orders : orderPendingList){
                String str = JSON.toJSONString(orders);
                String messageId = IdUtil.fastSimpleUUID();
                Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                log.info("[订单支付队列]成功，发送参数：{}",JSON.toJSON(str));
                rabbitTemplate.convertAndSend(RabbitMqConstant.DISTRIBUTION_ORDER_EXCHANGE,RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_PAY_ROUTING_KEY,message);
            }
        } else {
            log.info("[订单支付队列]失败，发送参数：{}",JSON.toJSON(orderPendingList));
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }
        return R.ok(ZSMallStatusCodeEnum.PAY_ORDER_SUCCESS);
    }

    private void payOrderBeforeCheck(List<Orders> orderNoList, String tenantId) {
        String errorMsg = "";
        for (Orders order : orderNoList) {
            LogisticsTypeEnum logisticsType = order.getLogisticsType();
            if (LogisticsTypeEnum.PickUp.equals(logisticsType) && (
                OrderSourceEnum.OPEN_API_ORDER.getValue().equals(order.getOrderSource()) ||
                OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource()) ||
                OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource()))) {
                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
                String logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
                if (CarrierTypeEnum.LTL.getValue().equals(logisticsCompanyName)) {

                } else {
                    // 校验数量商品数量与附件总页数是否一致
                    errorMsg = checkAttachment(order.getOrderNo(), errorMsg);
                }

            } else if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                // 需要label附件校验
                LambdaQueryWrapper<OrderAttachment> wrapper = new LambdaQueryWrapper<OrderAttachment>().eq(OrderAttachment::getOrderNo, order.getOrderNo());
                List<OrderAttachment> attachmentList = iOrderAttachmentService.list(wrapper);
                Map<OrderAttachmentTypeEnum, List<OrderAttachment>> typeEnumListMap = attachmentList.stream()
                                                                                                    .collect(Collectors.groupingBy(OrderAttachment::getAttachmentType));
                boolean notEmpty = CollUtil.isNotEmpty(typeEnumListMap);
                if (!notEmpty) {
                    errorMsg = order.getOrderNo() + "请上传ShippingLabel附件";
                }
                if (!typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ShippingLabel)) {
                    errorMsg = order.getOrderNo() + "请上传ShippingLabel附件";
                }

            }
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            throw new RStatusCodeException(errorMsg);
        }

    }

    /**
     * 功能描述：支付前校验附件页数
     *
     * @param orderNo  订单号
     * @param errorMsg 错误消息
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/12/03
     */
    private String checkAttachment(String orderNo,String errorMsg) {
        int pageSize = 0;
        Orders order = iOrdersService.getByOrderNo(orderNo);
        Integer totalQuantity = order.getTotalQuantity();
        LambdaQueryWrapper<OrderAttachment> wrapper = new LambdaQueryWrapper<OrderAttachment>().eq(OrderAttachment::getOrderNo, orderNo)
                                                                                               .eq(OrderAttachment::getDelFlag, 0);
        List<OrderAttachment> attachments = iOrderAttachmentService.list(wrapper);
        if (CollUtil.isNotEmpty(attachments)) {
            Map<OrderAttachmentTypeEnum, List<OrderAttachment>> typeEnumListMap = attachments.stream()
                                                                                             .collect(Collectors.groupingBy(OrderAttachment::getAttachmentType));
            boolean notEmpty = CollUtil.isNotEmpty(typeEnumListMap);

            if(!notEmpty){
                errorMsg = errorMsg+order.getOrderNo()+"请上传ShippingLabel附件";
            }
            if (!typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ShippingLabel)){
                errorMsg = errorMsg+order.getOrderNo()+"请上传ShippingLabel附件";
            }


            if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.CartonLabel)) {
                List<OrderAttachment> cartonLabelList = typeEnumListMap.get(OrderAttachmentTypeEnum.CartonLabel);
                if(CollUtil.isNotEmpty(cartonLabelList)&&cartonLabelList.size()>1){
                    errorMsg = errorMsg+ "CartonLabel attachment already exists!";
                }
            }
            if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.PalletLabel)) {
                List<OrderAttachment> palletLabelList = typeEnumListMap.get(OrderAttachmentTypeEnum.PalletLabel);
                if(CollUtil.isNotEmpty(palletLabelList)&&palletLabelList.size()>1){
                    errorMsg = errorMsg+"PalletLabel attachment already exists!";
                }
            }
            if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ItemLabel)) {
                List<OrderAttachment> itemLabelList = typeEnumListMap.get(OrderAttachmentTypeEnum.ItemLabel);
                if(CollUtil.isNotEmpty(itemLabelList)&&itemLabelList.size()>1){
                    errorMsg = errorMsg+"ItemLabel attachment already exists!";
                }
            }
            if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ShippingLabel)) {
                List<OrderAttachment> orderAttachmentList = typeEnumListMap.get(OrderAttachmentTypeEnum.ShippingLabel);
                if (CollUtil.isNotEmpty(orderAttachmentList)) {
                    for (OrderAttachment orderAttachment : orderAttachmentList) {
                        String attachmentShowUrl = orderAttachment.getAttachmentShowUrl();
                        InputStream oldShippingLabel = null;
                        try {
                            oldShippingLabel = ImagesUtil.downloadPdfAsStream(attachmentShowUrl);
                        } catch (Exception e) {
                            errorMsg = errorMsg+"File download failure!";
                        }
                        List<String> oldBase64List = null;
                        try {
                            oldBase64List = PDFUtil.splitPdfToBase64(oldShippingLabel, 1);
                            pageSize = pageSize + oldBase64List.size();
                        } catch (Exception e) {
                            errorMsg = errorMsg+"Failed to split the PDF file!";
                        }
                    }
                    if (pageSize > totalQuantity||pageSize < totalQuantity) {
                        errorMsg = errorMsg+"The number of pages of the ShippingLabel attachment is inconsistent with the number of goods!";
                    }
                }
            }
        }else {
            LambdaQueryWrapper<OrderAttachment> wrapper2 = new LambdaQueryWrapper<OrderAttachment>().eq(OrderAttachment::getOrderNo, order.getOrderNo());
            List<OrderAttachment> attachmentList = iOrderAttachmentService.list(wrapper2);
            Map<OrderAttachmentTypeEnum, List<OrderAttachment>> typeEnumListMap = attachmentList.stream()
                                                                                                .collect(Collectors.groupingBy(OrderAttachment::getAttachmentType));
            boolean notEmpty = CollUtil.isNotEmpty(typeEnumListMap);
            if (!notEmpty) {
                errorMsg = order.getOrderNo() + "请上传ShippingLabel附件";
            }
            if (!typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ShippingLabel)) {
                errorMsg = order.getOrderNo() + "请上传ShippingLabel附件";
            }
        }
        return errorMsg;
    }

    @Override
    public R<Void> payOrderForTest(OrderPayBo bo) throws Exception {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        String tenantId = loginUser.getTenantId();
        //      测试使用
        //      String tenantId = "DLTA1X9";
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

//        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(bo.getPaymentPassword()));

        List<Orders> orderList = iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed);
        if (CollUtil.size(orderNoList) == CollUtil.size(orderNoList)) {
            orderSupport.orderPayChainNotToErp(tenantId, orderList, true, true);
        } else {
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }

        return R.ok(ZSMallStatusCodeEnum.PAY_ORDER_SUCCESS);
    }

    public R<Void> payOrderForV3(OrderPayBo bo) throws Exception {
//        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
//        String tenantId = loginUser.getTenantId();
        //      测试使用
        String tenantId = bo.getTenantId();
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

//        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(bo.getPaymentPassword()));

        List<Orders> orderList = iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed);



        if (CollUtil.size(orderNoList) == CollUtil.size(orderNoList)) {
            orderSupport.orderPayChainNotToErpV3(tenantId, orderList, true, true);
        } else {
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }

        return R.ok(ZSMallStatusCodeEnum.PAY_ORDER_SUCCESS);
    }


    /**
     * 重新创建WMS订单
     *
     * @param bo
     * @return
     */
    @InMethodLog(value = "重新创建WMS订单")
    @Override
    public R<Void> recreateWMSOrder(OrderNoBo bo) {
        LoginHelper.getLoginUser(TenantType.Manager);

        String orderNo = bo.getOrderNo();
        if (StrUtil.isBlank(orderNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        List<Orders> ordersList = new ArrayList<>();
        Orders order = iOrdersService.getShippingOrder(orderNo, OrderStateType.Paid, ShippingOrderStateEnum.CreateFailed);
        if (order == null) {
            return R.fail(ZSMallStatusCodeEnum.ORDER_NOT_EXIST_OR_WMS_NOT_ERROR);
        }
        ordersList.add(order);

        List<OrderItemShippingRecord> shippingRecordList = iOrderItemShippingRecordService.getListByOrderNoAndType(orderNo, StockManagerEnum.BizArk);
        for (OrderItemShippingRecord shippingRecord : shippingRecordList) {
            WarehouseTypeEnum warehouseType = shippingRecord.getWarehouseType();
            Long shippingOrderId = shippingRecord.getId();
            String shippingNo = shippingRecord.getShippingNo();
            String supplierTenantId = shippingRecord.getSupplierTenantId();

            try {
                if (StrUtil.isNotBlank(shippingNo)) {
                    log.info("recreateWMSOrder shippingOrderId = {} shippingNo = {} sourceStoreCode = {}",
                        shippingOrderId, shippingNo, supplierTenantId);
                    ThirdWarehouseEvent.cancelOrder(StockManagerEnum.valueOf(warehouseType.name()), shippingNo);
                }
            } catch (Exception e) {
                log.error("重新创建WMS订单，先行取消订单异常 {}", e.getMessage(), e);
            }
        }
        iOrderItemShippingRecordService.removeBatchByIds(shippingRecordList);
        orderSupport.orderThirdWarehouseFollowUp(ordersList, true);
        return R.ok();
    }

    /**
     * 查询订单集合
     *
     * @param bo
     * @return
     */
    @Override
    public List<OrderPageVo> getOrders(OrderNosBo bo) {
        List<Orders> orders = iOrdersService.findByOrderNoInAndOrderStateIn(bo.getOrderNos(), new ArrayList<>());

        List<OrderPageVo> detailVos = new ArrayList<>();
        if (CollUtil.isNotEmpty(orders)) {
            orders.forEach(o -> {
                Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                           filter(oif -> oif.isThisImpl(o.getOrderType()))
                                                                                           .findFirst();
                OrderPageVo orderPageVo;
                if (orderHandleInterface.isPresent()) {
                    orderPageVo = orderHandleInterface.get().buildOrderBody(o, null);
                    detailVos.add(orderPageVo);
                } else {
                    orderPageVo = orderHandleInterfaces.stream().
                                                       filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst().get()
                                                       .buildOrderBody(o, null);
                    detailVos.add(orderPageVo);
                }

            });

        }

        return detailVos;
    }

    /**
     * 功能描述：设置订单业务字段
     *
     * @param orderReceiveFromThirdDTO 从ERP DTO接收订单
     * @param orders                 订单
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2024/01/11
     */
    @Override
    public Orders setOrderBusinessField(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        List<SaleOrderItemDTO> orderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        String logisticsType = orderReceiveFromThirdDTO.getLogisticsType();
        LogisticsTypeEnum logisticsTypeEnum = LogisticsTypeEnum.getLogisticsTypeEnumByName(logisticsType);
        SaleOrderItemDTO dto = orderItemsList.get(0);
        String erpSku = dto.getErpSku();
        orders.setLogisticsType(logisticsTypeEnum);
        ProductSkuPrice price ;
        BigDecimal originalProductAmount = BigDecimal.ZERO;

        BigDecimal platformProductAmount = BigDecimal.ZERO;

        BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
        BigDecimal originalPickAmount = BigDecimal.ZERO;

        BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
        BigDecimal platformPickAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount =BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        Long finalSiteId = orders.getSiteId();
        // 可能会有productSkuCode为null的情况,可能是没映射
        if(ObjectUtil.isNotEmpty(erpSku)) {
            if (ChannelTypeEnum.TikTok.name().equals(orders.getChannelType().name())) {
                price = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCodeAndSite(erpSku, finalSiteId));
            } else {
                price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSku(erpSku, finalSiteId));

            }
            ProductSku sku = TenantHelper.ignore(() -> iProductSkuService.getById(price.getProductSkuId()));
            Product product = TenantHelper.ignore(() -> iProductService.getById(sku.getProductId()));
            for (SaleOrderItemDTO itemDTO : orderItemsList) {
                // 数量*单价--此公式拿的是供应商录入价格
                originalProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                  .multiply(price.getOriginalUnitPrice());
                // 郭琪:定价公式 单价+操作费+配送费
                // 有会员定价优先使用会员定价

                RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), orderReceiveFromThirdDTO.getTenantId(), price.getProductSkuId(),finalSiteId );
                if (ObjectUtil.isNotEmpty(memberPrice)) {
                    originalDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                           .multiply(memberPrice.getOriginalUnitPrice()
                                                                                .add(memberPrice.getOriginalOperationFee())
                                                                                .add(memberPrice.getOriginalFinalDeliveryFee()));
                    originalPickAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                   .multiply(memberPrice.getOriginalUnitPrice()
                                                                        .add(memberPrice.getOriginalOperationFee()));
                    platformProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                      .multiply(memberPrice.getPlatformUnitPrice());
                    platformDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                           .multiply(memberPrice.getPlatformUnitPrice()
                                                                                .add(memberPrice.getPlatformOperationFee())
                                                                                .add(memberPrice.getPlatformFinalDeliveryFee()));

                    platformPickAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                   .multiply(memberPrice.getPlatformUnitPrice()
                                                                        .add(memberPrice.getPlatformOperationFee()));
                } else {
                    originalDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                           .multiply(price.getOriginalUnitPrice()
                                                                          .add(price.getOriginalOperationFee())
                                                                          .add(price.getOriginalFinalDeliveryFee()));
                    originalPickAmount = BigDecimal.valueOf(itemDTO.getQuantity()).multiply(price.getOriginalUnitPrice()
                                                                                                 .add(price.getOriginalOperationFee()));
                    platformProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                      .multiply(price.getPlatformUnitPrice());
                    platformDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                           .multiply(price.getPlatformUnitPrice()
                                                                          .add(price.getPlatformOperationFee())
                                                                          .add(price.getPlatformFinalDeliveryFee()));

                    platformPickAmount = BigDecimal.valueOf(itemDTO.getQuantity()).multiply(price.getPlatformUnitPrice()
                                                                                                 .add(price.getPlatformOperationFee()));
                }

            }
            BigDecimal platformActuralTotalAmount = getPlatformActuralTotalAmount(orderReceiveFromThirdDTO, orders);
            orders.setPlatformTotalPickUpPrice(platformPickAmount);
            orders.setPlatformTotalProductAmount(platformProductAmount);
//            orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);
            orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
            orders.setPlatformRefundExecutableAmount(platformActuralTotalAmount);
            orders.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            // 平台应付总金额
            orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
            // 平台实付总金额
            orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
            // 商品数量*尾程派送费
//            orders.setPlatformTotalFinalDeliveryFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getPlatformFinalDeliveryFee()));
            orders.setPlatformTotalOperationFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getPlatformOperationFee()));
            // 分销商金额 供应商金额
//            orders.setOriginalTotalFinalDeliveryFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getOriginalFinalDeliveryFee()));
            orders.setOriginalTotalOperationFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getOriginalOperationFee()));

            orders.setOriginalTotalProductAmount(originalProductAmount);

            orders.setOriginalTotalPickUpPrice(originalPickAmount);
            // 订单类型 drop就拿drop pick就拿pick
            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                originalActualTotalAmount = originalDropShippingAmount;
                originalRefundExecutableAmount = originalActualTotalAmount;
            }
            if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                originalActualTotalAmount = originalPickAmount;
                originalRefundExecutableAmount = originalPickAmount;
            }
            orders.setOriginalPayableTotalAmount(originalActualTotalAmount);
            orders.setOriginalActualTotalAmount(originalActualTotalAmount);
            orders.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);
//            orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);
            // 定金暂定0
            orders.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
//        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));

            orders.setTotalQuantity(orderReceiveFromThirdDTO.getTotalQuantity());
            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));


            // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping

            orders.setLogisticsType(logisticsTypeEnum);
            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());


            // 支付状态
            orders.setOrderState(OrderStateType.UnPaid);
            // 物流信息尚未同步 默认给 未发货
            orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orders.setCreateTime(orderReceiveFromThirdDTO.getCreateTime());
            orders.setLatestDeliveryTime(orderReceiveFromThirdDTO.getLatestDeliveryTime());
            orders.setPayTime(orderReceiveFromThirdDTO.getPaidTime());

            orders.setSplit(false);

        }else {
            BigDecimal platformActuralTotalAmount = getPlatformActuralTotalAmount(orderReceiveFromThirdDTO, orders);
            orders.setPlatformTotalPickUpPrice(platformPickAmount);
            orders.setPlatformTotalProductAmount(platformProductAmount);
//            orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);

            // 平台应付总金额
            orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
            // 平台实付总金额
            orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
            orders.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orders.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
//            供应商
            orders.setOriginalTotalProductAmount(BigDecimal.ZERO);

//            orders.setOriginalTotalFinalDeliveryFee(BigDecimal.ZERO);
            orders.setOriginalTotalOperationFee(BigDecimal.ZERO);

            orders.setOriginalTotalPickUpPrice(BigDecimal.ZERO);

//            orders.setOriginalTotalDropShippingPrice(BigDecimal.ZERO);
            orders.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            orders.setOriginalPayableTotalAmount(BigDecimal.ZERO);
            orders.setOriginalActualTotalAmount(BigDecimal.ZERO);
            orders.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
//            orders.setOriginalTotalDropShippingPrice(BigDecimal.ZERO);

            // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
//        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));

            orders.setTotalQuantity(orderReceiveFromThirdDTO.getTotalQuantity());
            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));

            // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping


            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());

            // 支付状态
            orders.setOrderState(OrderStateType.UnPaid);
//            orders.setPlatformTotalFinalDeliveryFee(BigDecimal.ZERO);
            orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
//            orders.setPlatformTotalFinalDeliveryFee(BigDecimal.ZERO);
            orders.setPlatformTotalOperationFee(BigDecimal.ZERO);
//            2024.8.12 更新为当前时间
            orders.setCreateTime(orderReceiveFromThirdDTO.getCreateTime());
            orders.setLatestDeliveryTime(orderReceiveFromThirdDTO.getLatestDeliveryTime());
            orders.setPayTime(orderReceiveFromThirdDTO.getPaidTime());

            orders.setSplit(false);
        }
        // 看看dealPrice那块
        return orders;
    }



    @Override
    public Orders setOrderBusinessFieldForOpen(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
        String regionCode = address.getRegionCode();
        Long siteId = null;
        SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
        siteId = site.getId();
        orders.setSiteId(siteId);
        orders.setCurrency(site.getCurrencyCode());
        orders.setCountryCode(site.getCountryCode());
        orders.setCurrencySymbol(site.getCurrencySymbol());

        List<SaleOrderItemDTO> orderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        SaleOrderItemDTO dto = orderItemsList.get(0);
//        String erpSku = dto.getErpSku();
        String itemNo = dto.getItemNo();
        ProductSkuPrice price ;
        orders.setOrderSource(OrderSourceEnum.OPEN_API_ORDER.getValue());

        Long finalSiteId = siteId;
        price = TenantHelper.ignore(() -> TenantHelper.ignore(()->productAboutManger.getProductPriceByItemNo(itemNo, finalSiteId)));
        BigDecimal originalProductAmount = BigDecimal.ZERO;

        BigDecimal platformProductAmount = BigDecimal.ZERO;

        BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
        BigDecimal originalPickAmount = BigDecimal.ZERO;

        BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
        BigDecimal platformPickAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount =BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        ProductSku sku =TenantHelper.ignore(()->iProductSkuService.getById(price.getProductSkuId())) ;
        Product product =TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;
//        BigDecimal dropShippingAmount = BigDecimal.ZERO;
        for (SaleOrderItemDTO itemDTO : orderItemsList) {
            // 数量*单价--此公式拿的是供应商录入价格
            originalProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                              .multiply(price.getOriginalUnitPrice());
            // 郭琪:定价公式 单价+操作费+配送费
            // 有会员定价优先使用会员定价
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), orderReceiveFromThirdDTO.getTenantId(), price.getProductSkuId(),siteId );
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                       .multiply(memberPrice.getOriginalUnitPrice()
                                                                            .add(memberPrice.getOriginalOperationFee())
                                                                            .add(memberPrice.getOriginalFinalDeliveryFee()));
                originalPickAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                               .multiply(memberPrice.getOriginalUnitPrice()
                                                                    .add(memberPrice.getOriginalOperationFee()));
                platformProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                  .multiply(memberPrice.getPlatformUnitPrice());
                platformDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                       .multiply(memberPrice.getPlatformUnitPrice()
                                                                            .add(memberPrice.getPlatformOperationFee())
                                                                            .add(memberPrice.getPlatformFinalDeliveryFee()));

                platformPickAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                               .multiply(memberPrice.getPlatformUnitPrice()
                                                                    .add(memberPrice.getPlatformOperationFee()));
            } else {
                originalDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                       .multiply(price.getOriginalUnitPrice()
                                                                      .add(price.getOriginalOperationFee())
                                                                      .add(price.getOriginalFinalDeliveryFee()));
                originalPickAmount = BigDecimal.valueOf(itemDTO.getQuantity()).multiply(price.getOriginalUnitPrice()
                                                                                             .add(price.getOriginalOperationFee()));
                platformProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                  .multiply(price.getPlatformUnitPrice());
                platformDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                       .multiply(price.getPlatformUnitPrice()
                                                                      .add(price.getPlatformOperationFee())
                                                                      .add(price.getPlatformFinalDeliveryFee()));

                platformPickAmount = BigDecimal.valueOf(itemDTO.getQuantity()).multiply(price.getPlatformUnitPrice()
                                                                                             .add(price.getPlatformOperationFee()));
            }

        }
        BigDecimal platformActuralTotalAmount = getPlatformActuralTotalAmount(orderReceiveFromThirdDTO, orders);
        orders.setPlatformTotalPickUpPrice(platformPickAmount);
        orders.setPlatformTotalProductAmount(platformProductAmount);
//        orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);

        orders.setPlatformRefundExecutableAmount(platformActuralTotalAmount);
        orders.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        // 平台应付总金额
        orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
        // 平台实付总金额
        orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
        // 商品数量*尾程派送费
//        orders.setPlatformTotalFinalDeliveryFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getPlatformFinalDeliveryFee()));
        orders.setPlatformTotalOperationFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getPlatformOperationFee()));
        // 分销商金额 供应商金额
//        orders.setOriginalTotalFinalDeliveryFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getOriginalFinalDeliveryFee()));
        orders.setOriginalTotalOperationFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getOriginalOperationFee()));

        orders.setOriginalTotalProductAmount(originalProductAmount);

        orders.setOriginalTotalPickUpPrice(originalPickAmount);
        // 订单类型 drop就拿drop pick就拿pick
        if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
            originalActualTotalAmount = originalDropShippingAmount;
            originalRefundExecutableAmount = originalActualTotalAmount;
        }
        if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
            originalActualTotalAmount = originalPickAmount;
            originalRefundExecutableAmount = originalPickAmount;
        }
        orders.setOriginalPayableTotalAmount(originalActualTotalAmount);
        orders.setOriginalActualTotalAmount(originalActualTotalAmount);
        orders.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);
//        orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);
        // 定金暂定0
        orders.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);

        orders.setTotalQuantity(orderReceiveFromThirdDTO.getTotalQuantity());



        // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping
        String logisticsType = orderReceiveFromThirdDTO.getLogisticsType();
        orders.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(logisticsType));


        // 支付状态
        orders.setOrderState(OrderStateType.UnPaid);
        orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orders.setCreateTime(orderReceiveFromThirdDTO.getCreateTime());
        orders.setLatestDeliveryTime(orderReceiveFromThirdDTO.getLatestDeliveryTime());
        orders.setPayTime(orderReceiveFromThirdDTO.getCreateTime());

        orders.setSplit(false);

        return orders;
    }


    @Override
    public Orders setOrderBusinessFieldForTemu(TemuOrderDTO temuOrderDTO, Orders orders) throws ParseException {
        List<TemuOrderItemDTO> items = temuOrderDTO.getItems();
        TemuOrderItemDTO temuOrderItemDTO = items.get(0);
//        String erpSku = temuOrderItemDTO.getSku();
//        ProductSkuPrice price = new ProductSkuPrice();
//        if(StringUtils.isNotEmpty(temuOrderItemDTO.getProductSkuCode())){
//            price = TenantHelper.ignore(() ->iProductSkuPriceService.queryByProductSkuCode(temuOrderItemDTO.getProductSkuCode()));
//        }
//        ProductSkuPrice price = TenantHelper.ignore(() -> iProductSkuService.getProductPriceBySellerSkuNull(temuOrderItemDTO.getSku()));
        BigDecimal originalProductAmount = BigDecimal.ZERO;

        BigDecimal platformProductAmount = BigDecimal.ZERO;

        BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
        BigDecimal originalPickAmount = BigDecimal.ZERO;

        BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
        BigDecimal platformPickAmount = BigDecimal.ZERO;
        Integer totalQuantity = 0;
//        if(null != price && null != price.getProductSkuId() ){
//            ProductSkuPrice finalPrice = price;
//            ProductSku sku =TenantHelper.ignore(()->iProductSkuService.getById(finalPrice.getProductSkuId())) ;
//            Product product =TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;
////        BigDecimal dropShippingAmount = BigDecimal.ZERO;
//            for (TemuOrderItemDTO temuOrderItemDTO1 : items) {
//                RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), temuOrderDTO.getTenantId(), price.getProductSkuId());
//                if(ObjectUtil.isNotEmpty(memberPrice)){
////                    originalDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getOriginalUnitPrice().add(memberPrice.getOriginalOperationFee()).add(memberPrice.getOriginalFinalDeliveryFee()));
////                    originalPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getOriginalUnitPrice().add(memberPrice.getOriginalOperationFee()));
////                    platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice());
////                    platformDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice().add(memberPrice.getPlatformOperationFee()).add(memberPrice.getPlatformFinalDeliveryFee()));;
////                    platformPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice().add(memberPrice.getPlatformOperationFee()));
//                }else{
//                    originalDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getOriginalUnitPrice().add(price.getOriginalOperationFee()).add(price.getOriginalFinalDeliveryFee()));
//                    originalPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getOriginalUnitPrice().add(price.getOriginalOperationFee()));
//                    platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice());
//                    platformDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice().add(price.getPlatformOperationFee()).add(price.getPlatformFinalDeliveryFee()));;
//                    platformPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice().add(price.getPlatformOperationFee()));
//                }
//                totalQuantity += temuOrderItemDTO1.getQuantity();
//            }
//        }else {
            for (TemuOrderItemDTO temuOrderItemDTO1 : items) {
                if(null != temuOrderItemDTO1.getPrice() && null != temuOrderItemDTO1.getQuantity()){
                    platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(temuOrderItemDTO1.getPrice());
                }
                if(null != temuOrderItemDTO1.getQuantity()){
                    totalQuantity += temuOrderItemDTO1.getQuantity();
                }
            }
//        }

        orders.setPlatformTotalPickUpPrice(platformPickAmount);
        orders.setPlatformTotalProductAmount(platformProductAmount);
        // 销售额金额
        orders.setPlatformActualTotalAmount(platformProductAmount);
//        orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);
        orders.setOriginalTotalProductAmount(originalProductAmount);

        orders.setOriginalTotalPickUpPrice(originalPickAmount);

//        orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);

        // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
//        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));

        orders.setTotalQuantity(totalQuantity);
        SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(temuOrderDTO.getCountry_code());
        if(null!= siteCountryCurrencyVo && null != siteCountryCurrencyVo.getCurrencyCode()){
            orders.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
        }
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));
//        BigDecimal platformActuralTotalAmount = temuOrderDTO.getAmount();
        // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping
        orders.setLogisticsType(temuOrderDTO.getLogisticsType());

        // 平台应付总金额
//        orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
        // 平台实付总金额
//        orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
        // 支付状态
        orders.setOrderState(OrderStateType.UnPaid);
        orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orders.setCreateTime(new Date());
//        orders.setPayTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(temuOrderDTO.getCreated()));
        orders.setLineOrderItemId(temuOrderDTO.getChannel_order_item_id());
        orders.setSplit(false);

        return orders;
    }

    @Override
    public Orders setOrderBusinessFieldForAmazonVc(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO,
                                                   Orders orders) throws ParseException {
        List<AmazonVCOrderItemMessageDTO> items = amazonVCOrderMessageDTO.getItems();
        AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO = items.get(0);
//        String erpSku = temuOrderItemDTO.getSku();
//        ProductSkuPrice price = new ProductSkuPrice();
//        if(StringUtils.isNotEmpty(temuOrderItemDTO.getProductSkuCode())){
//            price = TenantHelper.ignore(() ->iProductSkuPriceService.queryByProductSkuCode(temuOrderItemDTO.getProductSkuCode()));
//        }
//        ProductSkuPrice price = TenantHelper.ignore(() -> iProductSkuService.getProductPriceBySellerSkuNull(temuOrderItemDTO.getSku()));
        BigDecimal originalProductAmount = BigDecimal.ZERO;

        BigDecimal platformProductAmount = BigDecimal.ZERO;

        BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
        BigDecimal originalPickAmount = BigDecimal.ZERO;

        BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
        BigDecimal platformPickAmount = BigDecimal.ZERO;
        Integer totalQuantity = 0;
//        if(null != price && null != price.getProductSkuId() ){
//            ProductSkuPrice finalPrice = price;
//            ProductSku sku =TenantHelper.ignore(()->iProductSkuService.getById(finalPrice.getProductSkuId())) ;
//            Product product =TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;
////        BigDecimal dropShippingAmount = BigDecimal.ZERO;
//            for (TemuOrderItemDTO temuOrderItemDTO1 : items) {
//                RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), temuOrderDTO.getTenantId(), price.getProductSkuId());
//                if(ObjectUtil.isNotEmpty(memberPrice)){
////                    originalDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getOriginalUnitPrice().add(memberPrice.getOriginalOperationFee()).add(memberPrice.getOriginalFinalDeliveryFee()));
////                    originalPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getOriginalUnitPrice().add(memberPrice.getOriginalOperationFee()));
////                    platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice());
////                    platformDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice().add(memberPrice.getPlatformOperationFee()).add(memberPrice.getPlatformFinalDeliveryFee()));;
////                    platformPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice().add(memberPrice.getPlatformOperationFee()));
//                }else{
//                    originalDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getOriginalUnitPrice().add(price.getOriginalOperationFee()).add(price.getOriginalFinalDeliveryFee()));
//                    originalPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getOriginalUnitPrice().add(price.getOriginalOperationFee()));
//                    platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice());
//                    platformDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice().add(price.getPlatformOperationFee()).add(price.getPlatformFinalDeliveryFee()));;
//                    platformPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice().add(price.getPlatformOperationFee()));
//                }
//                totalQuantity += temuOrderItemDTO1.getQuantity();
//            }
//        }else {
        for (AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO1 : items) {
            if(null != amazonVCOrderItemMessageDTO1.getPrice() && null != amazonVCOrderItemMessageDTO1.getQuantity()){
                platformProductAmount = BigDecimal.valueOf(amazonVCOrderItemMessageDTO1.getQuantity()).multiply(amazonVCOrderItemMessageDTO1.getPrice());
            }
            if(null != amazonVCOrderItemMessageDTO1.getQuantity()){
                totalQuantity += amazonVCOrderItemMessageDTO1.getQuantity();
            }
        }
//        }

        orders.setPlatformTotalPickUpPrice(platformPickAmount);
        orders.setPlatformTotalProductAmount(platformProductAmount);
        // 销售额金额
        orders.setPlatformActualTotalAmount(platformProductAmount);
//        orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);
        orders.setOriginalTotalProductAmount(originalProductAmount);

        orders.setOriginalTotalPickUpPrice(originalPickAmount);

//        orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);

        // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
//        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));

        orders.setTotalQuantity(totalQuantity);
        orders.setCurrency(amazonVCOrderMessageDTO.getCurrency_code());
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));
//        BigDecimal platformActuralTotalAmount = temuOrderDTO.getAmount();
        // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping
        orders.setLogisticsType(amazonVCOrderMessageDTO.getLogisticsType());

        // 平台应付总金额
//        orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
        // 平台实付总金额
//        orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
        // 支付状态
        orders.setOrderState(OrderStateType.UnPaid);
        orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orders.setCreateTime(new Date());
//        orders.setPayTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(temuOrderDTO.getCreated()));
        orders.setLineOrderItemId(amazonVCOrderMessageDTO.getChannel_order_item_id());
        orders.setSplit(false);

        return orders;
    }

    @Override
    public Orders setOrderBusinessFieldForEc(EcOrderMessageDTO ecOrderMessageDTO,
                                                   Orders orders) throws ParseException {
        List<EcOrderItemMessageDTO> items = ecOrderMessageDTO.getItems();
        EcOrderItemMessageDTO ecOrderItemMessageDTO = items.get(0);
//        String erpSku = temuOrderItemDTO.getSku();
//        ProductSkuPrice price = new ProductSkuPrice();
//        if(StringUtils.isNotEmpty(temuOrderItemDTO.getProductSkuCode())){
//            price = TenantHelper.ignore(() ->iProductSkuPriceService.queryByProductSkuCode(temuOrderItemDTO.getProductSkuCode()));
//        }
//        ProductSkuPrice price = TenantHelper.ignore(() -> iProductSkuService.getProductPriceBySellerSkuNull(temuOrderItemDTO.getSku()));
        BigDecimal originalProductAmount = BigDecimal.ZERO;

        BigDecimal platformProductAmount = BigDecimal.ZERO;

        BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
        BigDecimal originalPickAmount = BigDecimal.ZERO;

        BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
        BigDecimal platformPickAmount = BigDecimal.ZERO;
        Integer totalQuantity = 0;
//        if(null != price && null != price.getProductSkuId() ){
//            ProductSkuPrice finalPrice = price;
//            ProductSku sku =TenantHelper.ignore(()->iProductSkuService.getById(finalPrice.getProductSkuId())) ;
//            Product product =TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;
////        BigDecimal dropShippingAmount = BigDecimal.ZERO;
//            for (TemuOrderItemDTO temuOrderItemDTO1 : items) {
//                RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), temuOrderDTO.getTenantId(), price.getProductSkuId());
//                if(ObjectUtil.isNotEmpty(memberPrice)){
////                    originalDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getOriginalUnitPrice().add(memberPrice.getOriginalOperationFee()).add(memberPrice.getOriginalFinalDeliveryFee()));
////                    originalPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getOriginalUnitPrice().add(memberPrice.getOriginalOperationFee()));
////                    platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice());
////                    platformDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice().add(memberPrice.getPlatformOperationFee()).add(memberPrice.getPlatformFinalDeliveryFee()));;
////                    platformPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(memberPrice.getPlatformUnitPrice().add(memberPrice.getPlatformOperationFee()));
//                }else{
//                    originalDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getOriginalUnitPrice().add(price.getOriginalOperationFee()).add(price.getOriginalFinalDeliveryFee()));
//                    originalPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getOriginalUnitPrice().add(price.getOriginalOperationFee()));
//                    platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice());
//                    platformDropShippingAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice().add(price.getPlatformOperationFee()).add(price.getPlatformFinalDeliveryFee()));;
//                    platformPickAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(price.getPlatformUnitPrice().add(price.getPlatformOperationFee()));
//                }
//                totalQuantity += temuOrderItemDTO1.getQuantity();
//            }
//        }else {
        for (EcOrderItemMessageDTO ecOrderItemMessageDTO1 : items) {
            if(null != ecOrderItemMessageDTO1.getSubtotal() && null != ecOrderItemMessageDTO1.getQuantity()){
                platformProductAmount = BigDecimal.valueOf(ecOrderItemMessageDTO1.getQuantity()).multiply(ecOrderItemMessageDTO1.getSubtotal());
            }
            if(null != ecOrderItemMessageDTO1.getQuantity()){
                totalQuantity += ecOrderItemMessageDTO1.getQuantity();
            }
        }
//        }

        orders.setPlatformTotalPickUpPrice(platformPickAmount);
        orders.setPlatformTotalProductAmount(platformProductAmount);
        // 销售额金额
        orders.setPlatformActualTotalAmount(platformProductAmount);
//        orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);
        orders.setOriginalTotalProductAmount(originalProductAmount);

        orders.setOriginalTotalPickUpPrice(originalPickAmount);

//        orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);

        // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
//        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));

        orders.setTotalQuantity(totalQuantity);
        orders.setCurrency(ecOrderMessageDTO.getCurrency_code());
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));
//        BigDecimal platformActuralTotalAmount = temuOrderDTO.getAmount();
        // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping
        orders.setLogisticsType(ecOrderMessageDTO.getLogisticsType());

        // 平台应付总金额
//        orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
        // 平台实付总金额
//        orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
        // 支付状态
        orders.setOrderState(OrderStateType.UnPaid);
        orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orders.setCreateTime(new Date());
//        orders.setPayTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(temuOrderDTO.getCreated()));
        orders.setLineOrderItemId(ecOrderMessageDTO.getChannel_order_item_id());
        orders.setSplit(false);

        return orders;
    }

    @Override
    public Orders setOrderBusinessFieldForAmazonSc(AmazonScOrderDTO amazonScOrderDTO,
                                                   Orders orders) throws ParseException {
        List<AmazonScOrderItemDTO> items = amazonScOrderDTO.getItems();
        AmazonScOrderItemDTO amazonScOrderItemDTO = items.get(0);
        BigDecimal originalProductAmount = BigDecimal.ZERO;

        BigDecimal platformProductAmount = BigDecimal.ZERO;
        // 销售额金额改用订单主表的Amount字段
        platformProductAmount = amazonScOrderDTO.getAmount();
        BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
        BigDecimal originalPickAmount = BigDecimal.ZERO;

        BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
        BigDecimal platformPickAmount = BigDecimal.ZERO;
        Integer totalQuantity = 0;
        for (AmazonScOrderItemDTO amazonScOrderItemDTO1 : items) {
//            if(null != amazonScOrderItemDTO1.getPrice() && null != amazonScOrderItemDTO1.getQuantity()){
//                platformProductAmount = BigDecimal.valueOf(temuOrderItemDTO1.getQuantity()).multiply(temuOrderItemDTO1.getPrice());
//            }
            if(null != amazonScOrderItemDTO1.getQuantity()){
                totalQuantity += amazonScOrderItemDTO1.getQuantity();
            }
        }

        orders.setPlatformTotalPickUpPrice(platformPickAmount);
        orders.setPlatformTotalProductAmount(platformProductAmount);
        // 销售额金额
        orders.setPlatformActualTotalAmount(platformProductAmount);
        orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);
        orders.setOriginalTotalProductAmount(originalProductAmount);

        orders.setOriginalTotalPickUpPrice(originalPickAmount);

        orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);


        orders.setTotalQuantity(totalQuantity);
        orders.setCurrency(amazonScOrderDTO.getCurrency_code());
        // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping
        orders.setLogisticsType(amazonScOrderDTO.getLogisticsType());
        // 支付状态
        orders.setOrderState(OrderStateType.UnPaid);
        // 物流信息尚未同步 默认给 已发货
        orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orders.setCreateTime(new Date());
        orders.setLineOrderItemId(amazonScOrderDTO.getChannel_order_item_id());
        orders.setSplit(false);
        return orders;
    }

    /**
     * 功能描述：获取平台Actural总金额
     *
     * @param orderReceiveFromThirdDTO 从第三个 DTO 接收订单
     * @param orders                   订单
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/07
     */
    private BigDecimal getPlatformActuralTotalAmount(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        BigDecimal platformActuralTotalAmount= BigDecimal.ZERO;
        if(ChannelTypeEnum.Erp.equals(orders.getChannelType())){
            List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
            SaleOrderItemDTO itemDTO = saleOrderItemsList.get(0);
            platformActuralTotalAmount = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
        }
        if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())){
            platformActuralTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
        }
        // order source
        Integer orderSource = orders.getOrderSource();
        if(OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource)){
            platformActuralTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
        }
        return platformActuralTotalAmount;
    }

    @Override
    public Orders setOrderBusinessField(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(() -> iTenantSalesChannelService.getByChannelFlag(erpSaleOrderDTO.getChannelFlag(), ChannelTypeEnum.MultiChannel.getValue()));
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(tenantSalesChannel.getTenantId());
        List<ErpSaleOrderItemDTO> orderItemsList = erpSaleOrderDTO.getSaleOrderItemsList();
        ErpSaleOrderItemDTO item = orderItemsList.get(0);
        String sellerSku = item.getSellerSku();
        Long siteId = orders.getSiteId();
        ProductSkuPrice price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSku(sellerSku, siteId));
        BigDecimal amount = BigDecimal.ZERO;
        for (ErpSaleOrderItemDTO itemDTO : orderItemsList) {
            // 数量*单价
            amount = BigDecimal.valueOf(itemDTO.getQuantity()).multiply(price.getOriginalUnitPrice());
        }
        orders.setTotalQuantity(item.getQuantity());
        orders.setCurrency(erpSaleOrderDTO.getCurrencyCode());
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));
//        orders.setPlatformActualTotalAmount(amount);
        // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping
        // 多渠道的 默认代发
        String logisticsType = "DropShipping";
        LogisticsTypeEnum logisticsTypeEnum = LogisticsTypeEnum.getLogisticsTypeEnumByName(logisticsType);
        orders.setLogisticsType(logisticsTypeEnum);
        orders.setCurrency(erpSaleOrderDTO.getCurrencyCode());

        orders.setOriginalTotalProductAmount(amount);
//        orders.setOriginalTotalDropShippingPrice(amount);
        // 此字段存平台交易金额
        orders.setPlatformPayableTotalAmount(item.getChannelTotalSalesAmount());
        orders.setPlatformActualTotalAmount(item.getChannelTotalSalesAmount());

        // 支付状态
        orders.setOrderState(OrderStateType.UnPaid);
        orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        // 如果是dropShipping需要加上费用
        if (LogisticsTypeEnum.DropShipping.equals(logisticsTypeEnum)) {
            // 判断tenant的类型
            if (TenantType.Distributor.name().equals(sysTenantVo.getTenantType())) {
                amount = amount.add(price.getPlatformOperationFee()).add(price.getPlatformFinalDeliveryFee());
            } else if (TenantType.Supplier.name().equals(sysTenantVo.getTenantType())) {
                amount = amount.add(price.getOriginalOperationFee().add(price.getOriginalFinalDeliveryFee()));
            }
        }
        return orders;
    }

    @Override
    public Orders setOrderTagSystem(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        // 看系统内的状态

        // 会影响售后,国外的单不能售后,其余用于搜索查询过滤,默认给了普通订单
        orders.setOrderType(OrderType.Normal);
        // 从渠道店铺拿对应的分销商租户id
        orders.setLineOrderItemId(orderReceiveFromThirdDTO.getLineOrderItemId());
        orders.setTenantId(orderReceiveFromThirdDTO.getTenantId());
        return orders;
    }

    @Override
    public Orders setOrderTagSystemByTemu(TemuOrderDTO temuOrderDTO, Orders orders) {
        // 看系统内的状态

        // 会影响售后,国外的单不能售后,其余用于搜索查询过滤,默认给了普通订单
        orders.setOrderType(OrderType.Normal);
        // 从渠道店铺拿对应的分销商租户id
        orders.setLineOrderItemId(temuOrderDTO.getChannel_order_item_id());
        orders.setTenantId(temuOrderDTO.getTenantId());
        return orders;
    }

    @Override
    public Orders setOrderTagSystemByAmazonVc(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders) {
        // 会影响售后,国外的单不能售后,其余用于搜索查询过滤,默认给了普通订单
        orders.setOrderType(OrderType.Normal);
        // 从渠道店铺拿对应的分销商租户id
        orders.setLineOrderItemId(amazonVCOrderMessageDTO.getChannel_order_item_id());
        orders.setTenantId(amazonVCOrderMessageDTO.getTenantId());
        return orders;
    }

    @Override
    public Orders setOrderTagSystemByEc(EcOrderMessageDTO ecOrderMessageDTO, Orders orders) {
        // 会影响售后,国外的单不能售后,其余用于搜索查询过滤,默认给了普通订单
        orders.setOrderType(OrderType.Normal);
        // 从渠道店铺拿对应的分销商租户id
        orders.setLineOrderItemId(ecOrderMessageDTO.getChannel_order_item_id());
        orders.setTenantId(ecOrderMessageDTO.getTenantId());
        return orders;
    }

    @Override
    public Orders setOrderTagSystemByAmazonSc(AmazonScOrderDTO amazonScOrderDTO, Orders orders) {
        // 会影响售后,国外的单不能售后,其余用于搜索查询过滤,默认给了普通订单
        orders.setOrderType(OrderType.Normal);
        // 从渠道店铺拿对应的分销商租户id
        orders.setLineOrderItemId(amazonScOrderDTO.getChannel_order_item_id());
        orders.setTenantId(amazonScOrderDTO.getTenantId());
        return orders;
    }

    @Override
    public Orders setOrderTagSystem(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        orders.setOrderType(OrderType.Normal);
        // 分销商的id 分销商和 要从店铺同步
        // 多渠道拿的是
        TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(() -> iTenantSalesChannelService.getByChannelFlag(erpSaleOrderDTO.getChannelFlag(), ChannelTypeEnum.MultiChannel.getValue()));

        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("店铺信息尚未同步:" + erpSaleOrderDTO.getChannelFlag());
        }
        orders.setTenantId(tenantSalesChannel.getTenantId());
        return orders;
    }

    @Override
    public Orders setChannelTag(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {

        orders.setChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo());
        // 要通过flag和店铺表内的channelAlias 这里得拿systenant
//        SysTenant sysTenant = sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(), orders.getChannelType().name());
        if (ObjectUtil.isNull(tenantSalesChannel) || ObjectUtil.isEmpty(tenantSalesChannel.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + orderReceiveFromThirdDTO.getThirdChannelFlag() + ",请联系管理员手动维护");
        }
        // 设置 channelOrderNoName
        if(null != orderReceiveFromThirdDTO.getIsMultiple() && orderReceiveFromThirdDTO.getIsMultiple()){
            String channelOrderName;
            // 将orderNo后四位截取
            String lastFourChars = null;
            if (StringUtils.isNotEmpty(orders.getOrderNo()) && orders.getOrderNo().length() >= 4) {
                // 使用substring方法截取最后四位
                lastFourChars = orders.getOrderNo().substring(orders.getOrderNo().length() - 4);
            }
            channelOrderName = orderReceiveFromThirdDTO.getOrderNo() + "-" + lastFourChars;
            orders.setChannelOrderName(channelOrderName);
        }else {
            orders.setChannelOrderName(orderReceiveFromThirdDTO.getOrderNo());
        }
        orders.setChannelId(tenantSalesChannel.getId());
        orders.setChannelAlias(orders.getChannelType().name() + ":" + orderReceiveFromThirdDTO.getThirdChannelFlag());
        orders.setChannelOrderName(orderReceiveFromThirdDTO.getOrderNo());
        orders.setChannelOrderTime(orderReceiveFromThirdDTO.getCreateTime());
        orders.setLatestDeliveryTime(orderReceiveFromThirdDTO.getLatestDeliveryTime());
        orders.setPayTime(orderReceiveFromThirdDTO.getPaidTime());
        orders.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        return orders;
    }

    @Override
    public Orders setChannelTagForOpen(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        orders.setChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo());
        // 要通过flag和店铺表内的channelAlias 这里得拿systenant
        SysTenant sysTenant = sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(),orders.getTenantId());
//        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(), orders.getChannelType().name());
        if (ObjectUtil.isNull(sysTenant) || ObjectUtil.isEmpty(sysTenant.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + orderReceiveFromThirdDTO.getThirdChannelFlag() + ",请联系管理员手动维护");
        }
        orders.setChannelId(null);
        if (ObjectUtil.isEmpty(orders.getChannelType())){
            orders.setChannelAlias("others" + ":" + orderReceiveFromThirdDTO.getThirdChannelFlag());
        }else {
            orders.setChannelAlias(orders.getChannelType().name() + ":" + orderReceiveFromThirdDTO.getThirdChannelFlag());
        }

        orders.setChannelOrderName(orderReceiveFromThirdDTO.getOrderNo());
        orders.setChannelOrderTime(orderReceiveFromThirdDTO.getCreateTime());
        orders.setLatestDeliveryTime(orderReceiveFromThirdDTO.getLatestDeliveryTime());
        orders.setPayTime(orderReceiveFromThirdDTO.getPaidTime());
        orders.setOrderSource(OrderSourceEnum.OPEN_API_ORDER.getValue());
        return orders;
    }

    @Override
    public Orders setChannelTagForTemu(TemuOrderDTO temuOrderDTO, Orders orders) throws ParseException {
        orders.setChannelType(temuOrderDTO.getChannelType());
        orders.setChannelOrderNo(temuOrderDTO.getOrdernum());
        // 要通过flag和店铺表内的channelAlias 这里得拿systenant
//        SysTenant sysTenant = sysTenantService.getTenantByThirdChannelFlag(temuOrderDTO.getAccount_id());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(temuOrderDTO.getAccount_id(), orders.getChannelType().name());
        if (ObjectUtil.isNull(tenantSalesChannel) || ObjectUtil.isEmpty(tenantSalesChannel.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + temuOrderDTO.getAccount_id() + ",请联系管理员手动维护");
        }
        orders.setChannelId(tenantSalesChannel.getId());
        if (ObjectUtil.isEmpty(orders.getChannelType())){
            orders.setChannelAlias("others" + ":" + temuOrderDTO.getAccount_id());
        }else {
            orders.setChannelAlias(orders.getChannelType().name() + ":" + temuOrderDTO.getAccount_id());
        }
        // 设置 channelOrderNoName
        if(null != temuOrderDTO.getIsMultiple() && temuOrderDTO.getIsMultiple()){
            String channelOrderName;
            // 将orderNo后四位截取
            String lastFourChars = null;
            if (StringUtils.isNotEmpty(orders.getOrderNo()) && orders.getOrderNo().length() >= 4) {
                // 使用substring方法截取最后四位
                lastFourChars = orders.getOrderNo().substring(orders.getOrderNo().length() - 4);
            }
            channelOrderName = temuOrderDTO.getOrdernum() + "-" + lastFourChars;
            orders.setChannelOrderName(channelOrderName);
        }else {
            orders.setChannelOrderName(temuOrderDTO.getOrdernum());
        }
        if(StringUtils.isNotEmpty(temuOrderDTO.getCreated())){
// 转化时区
//            ZonedDateTime usTime = ZonedDateTime.of(LocalDateTime.parse(temuOrderDTO.getCreated(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), ZoneId.of("America/Los_Angeles"));
//            ZonedDateTime chinaTime = usTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
//            orders.setChannelOrderTime(Date.from(chinaTime.toInstant()));
            orders.setChannelOrderTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(temuOrderDTO.getCreated()));
        }
        orders.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());

        return orders;
    }

    @Override
    public Orders setChannelTagForAmazonVc(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO,
                                           Orders orders) throws ParseException {
        orders.setChannelType(amazonVCOrderMessageDTO.getChannelType());
        orders.setChannelOrderNo(amazonVCOrderMessageDTO.getOrdernum());
        // 要通过flag和店铺表内的channelAlias 这里得拿systenant
//        SysTenant sysTenant = sysTenantService.getTenantByThirdChannelFlag(temuOrderDTO.getAccount_id());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(amazonVCOrderMessageDTO.getAccount_id(), orders.getChannelType().name());
        if (ObjectUtil.isNull(tenantSalesChannel) || ObjectUtil.isEmpty(tenantSalesChannel.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + amazonVCOrderMessageDTO.getAccount_id() + ",请联系管理员手动维护");
        }
        orders.setChannelId(tenantSalesChannel.getId());
        if (ObjectUtil.isEmpty(orders.getChannelType())){
            orders.setChannelAlias("others" + ":" + amazonVCOrderMessageDTO.getAccount_id());
        }else {
            orders.setChannelAlias(orders.getChannelType().name() + ":" + amazonVCOrderMessageDTO.getAccount_id());
        }
        // 设置 channelOrderNoName
        if(null != amazonVCOrderMessageDTO.getIsMultiple() && amazonVCOrderMessageDTO.getIsMultiple()){
            String channelOrderName;
            // 将orderNo后四位截取
            String lastFourChars = null;
            if (StringUtils.isNotEmpty(orders.getOrderNo()) && orders.getOrderNo().length() >= 4) {
                // 使用substring方法截取最后四位
                lastFourChars = orders.getOrderNo().substring(orders.getOrderNo().length() - 4);
            }
            channelOrderName = amazonVCOrderMessageDTO.getOrdernum() + "-" + lastFourChars;
            orders.setChannelOrderName(channelOrderName);
        }else {
            orders.setChannelOrderName(amazonVCOrderMessageDTO.getOrdernum());
        }
        if(StringUtils.isNotEmpty(amazonVCOrderMessageDTO.getCreated())){
// 转化时区
//            ZonedDateTime usTime = ZonedDateTime.of(LocalDateTime.parse(temuOrderDTO.getCreated(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), ZoneId.of("America/Los_Angeles"));
//            ZonedDateTime chinaTime = usTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
//            orders.setChannelOrderTime(Date.from(chinaTime.toInstant()));
            orders.setChannelOrderTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(amazonVCOrderMessageDTO.getCreated()));
        }
        orders.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        orders.setChannelWarehouseCode(amazonVCOrderMessageDTO.getWarehouse_code());
        return orders;
    }

    @Override
    public Orders setChannelTagForEc(EcOrderMessageDTO ecOrderMessageDTO,
                                           Orders orders) throws ParseException {
        orders.setChannelType(ecOrderMessageDTO.getChannelType());
        orders.setChannelOrderNo(ecOrderMessageDTO.getOrdernum());
        // 要通过flag和店铺表内的channelAlias 这里得拿systenant
//        SysTenant sysTenant = sysTenantService.getTenantByThirdChannelFlag(temuOrderDTO.getAccount_id());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(ecOrderMessageDTO.getAccount_id(), orders.getChannelType().name());
        if (ObjectUtil.isNull(tenantSalesChannel) || ObjectUtil.isEmpty(tenantSalesChannel.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + ecOrderMessageDTO.getAccount_id() + ",请联系管理员手动维护");
        }
        orders.setChannelId(tenantSalesChannel.getId());
        if (ObjectUtil.isEmpty(orders.getChannelType())){
            orders.setChannelAlias("others" + ":" + ecOrderMessageDTO.getAccount_id());
        }else {
            orders.setChannelAlias(orders.getChannelType().name() + ":" + ecOrderMessageDTO.getAccount_id());
        }
        // 设置 channelOrderNoName
        if(null != ecOrderMessageDTO.getIsMultiple() && ecOrderMessageDTO.getIsMultiple()){
            String channelOrderName;
            // 将orderNo后四位截取
            String lastFourChars = null;
            if (StringUtils.isNotEmpty(orders.getOrderNo()) && orders.getOrderNo().length() >= 4) {
                // 使用substring方法截取最后四位
                lastFourChars = orders.getOrderNo().substring(orders.getOrderNo().length() - 4);
            }
            channelOrderName = ecOrderMessageDTO.getOrdernum() + "-" + lastFourChars;
            orders.setChannelOrderName(channelOrderName);
        }else {
            orders.setChannelOrderName(ecOrderMessageDTO.getOrdernum());
        }
        if(StringUtils.isNotEmpty(ecOrderMessageDTO.getCreated())){
// 转化时区
//            ZonedDateTime usTime = ZonedDateTime.of(LocalDateTime.parse(temuOrderDTO.getCreated(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), ZoneId.of("America/Los_Angeles"));
//            ZonedDateTime chinaTime = usTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
//            orders.setChannelOrderTime(Date.from(chinaTime.toInstant()));
            orders.setChannelOrderTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(ecOrderMessageDTO.getCreated()));
        }
        orders.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        orders.setChannelWarehouseCode(ecOrderMessageDTO.getWarehouse_code());
        return orders;
    }
    @Override
    public Orders setChannelTagForAmazonSc(AmazonScOrderDTO amazonScOrderDTO, Orders orders) throws ParseException {
        orders.setChannelType(amazonScOrderDTO.getChannelType());
        orders.setChannelOrderNo(amazonScOrderDTO.getOrdernum());
        // 要通过flag和店铺表内的channelAlias 这里得拿systenant
//        SysTenant sysTenant = sysTenantService.getTenantByThirdChannelFlag(temuOrderDTO.getAccount_id());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(amazonScOrderDTO.getAccount_id(), orders.getChannelType().name());
        if (ObjectUtil.isNull(tenantSalesChannel) || ObjectUtil.isEmpty(tenantSalesChannel.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + amazonScOrderDTO.getAccount_id() + ",请联系管理员手动维护");
        }
        orders.setChannelId(tenantSalesChannel.getId());
        if (ObjectUtil.isEmpty(orders.getChannelType())){
            orders.setChannelAlias("others" + ":" + amazonScOrderDTO.getAccount_id());
        }else {
            orders.setChannelAlias(orders.getChannelType().name() + ":" + amazonScOrderDTO.getAccount_id());
        }
        // 设置 channelOrderNoName
        if(null != amazonScOrderDTO.getIsMultiple() && amazonScOrderDTO.getIsMultiple()){
            String channelOrderName;
            // 将orderNo后四位截取
            String lastFourChars = null;
            if (StringUtils.isNotEmpty(orders.getOrderNo()) && orders.getOrderNo().length() >= 4) {
                // 使用substring方法截取最后四位
                lastFourChars = orders.getOrderNo().substring(orders.getOrderNo().length() - 4);
            }
            channelOrderName = amazonScOrderDTO.getOrdernum() + "-" + lastFourChars;
            orders.setChannelOrderName(channelOrderName);
        }else {
            orders.setChannelOrderName(amazonScOrderDTO.getOrdernum());
        }
        if(StringUtils.isNotEmpty(amazonScOrderDTO.getCreated())){
// 转化时区
//            ZonedDateTime usTime = ZonedDateTime.of(LocalDateTime.parse(temuOrderDTO.getCreated(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), ZoneId.of("America/Los_Angeles"));
//            ZonedDateTime chinaTime = usTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
//            orders.setChannelOrderTime(Date.from(chinaTime.toInstant()));
            orders.setChannelOrderTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(amazonScOrderDTO.getCreated()));
        }
        orders.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());

        return orders;
    }

    @Override
    public Orders setChannelTag(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders) {
        orders.setChannelType(ChannelTypeEnum.MultiChannel);
        orders.setChannelOrderNo(erpSaleOrderDTO.getOrderNo());
        TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(() -> iTenantSalesChannelService.getByChannelFlag(erpSaleOrderDTO.getChannelFlag(), ChannelTypeEnum.MultiChannel.getValue()));

        if (ObjectUtil.isNull(tenantSalesChannel) || ObjectUtil.isEmpty(tenantSalesChannel.getTenantId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + erpSaleOrderDTO.getChannelFlag() + ",请联系管理员手动维护");
        }
        orders.setChannelId(tenantSalesChannel.getId());
        orders.setChannelAlias(ChannelTypeEnum.MultiChannel + ":" + erpSaleOrderDTO.getChannelFlag());
        orders.setChannelOrderName(erpSaleOrderDTO.getOrderNo());
        orders.setChannelOrderTime(TimeUtil.localDateTimeToDate(erpSaleOrderDTO.getChannelCreated()));
        return orders;
    }

    @Override
    public boolean payOrderForErp(OrderPayBo bo, String tenantId, boolean isGenuinePay, boolean isNeedCreate) throws Exception {
        // 拿订单租户
        AtomicBoolean result = new AtomicBoolean(false);
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            throw new OrderPayException("订单数据异常");
        }

        List<Orders> orderList = TenantHelper.ignore(() -> iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed));
        if (CollUtil.size(orderList) == CollUtil.size(orderNoList)) {
            TenantHelper.ignore(() -> {
                try {
                    // 分割orderNoList 为30一组
                    if(CollUtil.isNotEmpty(orderNoList)){
                        List<Orders> ordersList = iOrdersService.queryByOrderNoList(orderNoList);
                        Map<Integer, List<Orders>> groupedOrders = splitListIntoGroups(ordersList, 30);
                        for (Map.Entry<Integer, List<Orders>> entry : groupedOrders.entrySet()){
                            List<Orders> ordersInGroup = entry.getValue();
                            try {
                                if(CollUtil.isNotEmpty(ordersList)){
                                    orderSupport.orderPayChain(tenantId, ordersInGroup, isNeedCreate, isGenuinePay);
                                }
                            }catch (Exception e){
                                log.error("支付订单异常", e);
                                log.error("支付订单异常,异常订单号存在于:{}",ordersInGroup.stream().map(Orders::getOrderNo).collect(Collectors.toList()));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("支付订单异常", e);
                    throw new RuntimeException(e);
                }
            });

        } else {
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }

        return result.get();
    }

    public <T> Map<Integer, List<T>> splitListIntoGroups(List<T> list, int size) {
        Map<Integer, List<T>> result = new HashMap<>();
        for (int i = 0; i < list.size(); i += size) {
            int groupKey = i / size; // 使用整除来生成组键
            int end = Math.min(i + size, list.size()); // 确保不会超出列表长度
            List<T> subList = list.subList(i, end);
            result.put(groupKey, subList);
        }
        return result;
    }
    @Override
    public boolean payOrderOnlyErp(OrderPayBo bo, String tenantId, boolean isGenuinePay,
                                   boolean isNeedCreate) throws OrderPayException, Exception {
        // 拿订单租户
        AtomicBoolean result = new AtomicBoolean(false);
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            throw new OrderPayException("订单数据异常");
        }
//        String orderNo = orderNoList.get(0);
//        Orders order = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(orderNo));
//        SpringUtils.context().publishEvent(new CheckPaymentPasswordEventNoLogin(bo.getPaymentPassword(),tenantId));

        List<Orders> orderList = TenantHelper.ignore(() -> iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed));
        if (CollUtil.size(orderList) == CollUtil.size(orderNoList)) {
            TenantHelper.ignore(() -> {
                try {
                    orderSupport.orderPayChainOnlyErp(tenantId, orderList, isNeedCreate, isGenuinePay);
                } catch (Exception e) {
                    log.error("支付订单异常", e);
                    throw new RuntimeException(e);
                }
            });

        } else {
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }

        return result.get();
    }

    @Override
    public boolean payOrderForDistribution(OrderPayBo bo, String tenantId, boolean isGenuinePay,
                                           boolean isNeedCreate) throws OrderPayException, Exception {
        // 拿订单租户
        AtomicBoolean result = new AtomicBoolean(false);
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            throw new OrderPayException("订单数据异常");
        }
//        String orderNo = orderNoList.get(0);
//        Orders order = TenantHelper.ignore(() -> iOrdersService.getByOrderNo(orderNo));
//        SpringUtils.context().publishEvent(new CheckPaymentPasswordEventNoLogin(bo.getPaymentPassword(),tenantId));

        List<Orders> orderList = TenantHelper.ignore(() -> iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed));
        if (CollUtil.size(orderList) == CollUtil.size(orderNoList)) {
            TenantHelper.ignore(() -> {
                try {
                    orderSupport.orderPayChain(tenantId, orderList, isNeedCreate, isGenuinePay);
                } catch (Exception e) {
                    log.error("支付订单异常", e);
                    throw new RuntimeException(e);
                }
            });

        } else {
            throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
        }

        return result.get();
    }

    @Override
    public R<Void> export(OrdersPageBo dto, HttpServletResponse response, PageQuery pageQuery)  {

        List<OrderExportVo> orderDetailVos = new ArrayList<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("订单详情导出前信息查询归纳");

        orderDetailVos = exportForDetails(dto,pageQuery);
        stopWatch.stop();

        ExcelUtil.exportExcel(orderDetailVos, "订单详情", OrderExportVo.class, response, Boolean.TRUE);


        return R.ok();
    }

    /**
     * 功能描述：导出详细信息
     *
     * @param dto       @return {@link List }<{@link OrderExportVo }>
     * @param pageQuery
     * <AUTHOR>
     * @date 2024/01/23
     */
    private List<OrderExportVo> exportForDetails(OrdersPageBo dto, PageQuery pageQuery)  {

        return queryByQuery(dto,pageQuery);
    }

    /**
     * 功能描述：按开始和结束时间查询
     *
     * @param dto       @return {@link OrderDetailVo }
     * @param pageQuery
     * <AUTHOR>
     * @date 2024/01/26
     */
    @SneakyThrows
    private List<OrderExportVo> queryByQuery(OrdersPageBo dto, PageQuery pageQuery)  {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
//        TableDataInfo<OrderPageVo> orderPage = queryPageList(dto, pageQuery);
        if (StrUtil.isNotBlank(dto.getQueryType()) && StrUtil.isNotBlank(dto.getQueryValue())) {
            String value = dto.getQueryValue();
            switch (dto.getQueryType()) {
                case "OrderNo":
                    dto.setOrderNo(value);
                    break;
                case "ChannelOrderName":
                    dto.setChannelOrderNo(value);
                    break;
                case "Sku":
                    dto.setSku(value);
                    break;
                case "ProductName":
                    dto.setProductName(value);
                    break;
                case "ProductSkuCode":
                    dto.setItemNo(value);
                    break;
                case "TrackingNo":
                    dto.setTrackingNo(value);
                    break;
                case "ActivityID":
                    dto.setActivityCode(value);
                    break;
                case "SupplierTenantId":
                    dto.setSupplierTenantId(value);
                    break;
                case "TenantId":
                    dto.setDistributorTenantId(value);
                    break;
                default:
                    break;
            }
        }


        Integer limit = Integer.MAX_VALUE;
        Integer page = 1;
        String sortValue = dto.getSortValue();
        Page<Orders> ordersVoPage = new Page<>(page, limit);
        OrderItem orderITem;
        switch (sortValue) {
            case "createTimeAsc":
                orderITem = OrderItem.asc("create_time");
                break;
            case "orderNoAsc":
                orderITem = OrderItem.asc("order_no");
                break;
            case "orderNoDesc":
                orderITem = OrderItem.desc("order_no");
                break;
            case "priceAsc":
                orderITem = LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)
                    ? OrderItem.asc("platform_payable_total_amount")
                    : OrderItem.asc("original_payable_total_amount");
                break;
            case "priceDesc":
                orderITem = LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)
                    ? OrderItem.desc("platform_payable_total_amount")
                    : OrderItem.desc("original_payable_total_amount");
                break;
            default:
                orderITem = OrderItem.desc("create_time");
                break;

        }
        //订单状态
        List<String> orderStateTypes = new ArrayList<>();
        String orderState = dto.getOrderState();
        if (StrUtil.isNotBlank(orderState)) {
            if (StrUtil.equals(orderState, OrderStateType.UnPaid.name())) {
                orderStateTypes.add(OrderStateType.UnPaid.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Failed.name())) {
                orderStateTypes.add(OrderStateType.Failed.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Refunded.name())) {
                orderStateTypes.add(OrderStateType.Refunded.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.UnDispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.Dispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Canceled.name())) {
                orderStateTypes.add(OrderStateType.Canceled.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.Fulfilled.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Pending.name())) {
                orderStateTypes.add(OrderStateType.Pending.name());
            }
        }
        dto.setOrderStates(orderStateTypes);
        ordersVoPage.addOrder(orderITem);
        Page<Orders> orderPage = iOrdersService.queryPageList(dto, ordersVoPage);
        List<Orders> orders = orderPage.getRecords();
        //        List<Orders> orders = iOrdersService.getByQuery(dto);
        stopWatch.stop();
        List<OrderExportVo> orderExportVos = getOrderExportVos(orders);
        // orders中的orderNo比orderExportVos中的orderNo多的部分是没有查询到的,打印出来
        List<String> orderNos = orderExportVos.stream().map(OrderExportVo::getOrderId).collect(Collectors.toList());
        List<String> orderNos1 = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        List<String> collect = orderNos1.stream().filter(item -> !orderNos.contains(item)).collect(Collectors.toList());
        log.info("orderNos1中比orderNos多的部分{}",collect);
        // 拿到缺失的订单号找到orders中的订单,放入新的集合,再次使用getOrderExportVos方法
        List<Orders> collect1 = orders.stream().filter(item -> collect.contains(item.getOrderNo())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect1)) {
            List<OrderExportVo> orderExportVos1 = getOrderExportVos(collect1);
            orderExportVos.addAll(orderExportVos1);
        }
        return orderExportVos;
    }

    private List<OrderExportVo> getOrderExportVos(List<Orders> orders) throws InterruptedException {
        ThreadPoolExecutor executor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
        CountDownLatch downLatch = new CountDownLatch(orders.size());
        List<OrderExportVo> orderExportVos = new ArrayList<>();
        orders.forEach(item->{
            // 开线程池处理包装
            executor.submit(() -> {

                try {
                    Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                               filter(oif -> oif.isThisImpl(item.getOrderType()))
                                                                                               .findFirst();

                    OrderDetailVo orderDetailVo;
                    if (orderHandleInterface.isPresent()) {
                        orderDetailVo = orderHandleInterface.get().buildOrderDetailForExport(item);
                    } else {
                        orderDetailVo = orderHandleInterfaces.stream().
                                                             filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst()
                                                             .get()
                                                             .buildOrderDetail(item);
                    }
                    OrderExportVo vo = new OrderExportVo();
                    voWrapper(orderDetailVo, vo);
                    orderExportVos.add(vo);
                }catch (Exception e){
                    log.error("出现异常:{}",e.getMessage());
                    e.printStackTrace();
                    throw new RuntimeException();
                }finally {
                    downLatch.countDown();
                }

            });
        });
        downLatch.await();
        return orderExportVos;
    }


    private void voWrapper(OrderDetailVo orderDetailVo, OrderExportVo vo) {
        OrderItemDetailVo itemDetailVo = orderDetailVo.getOrderItemsCategory().get(0);
        OrderItemVo orderItem = itemDetailVo.getOrderItem();
        Orders orders = iOrdersService.getOne(new LambdaQueryWrapper<Orders>().eq(Orders::getOrderNo, orderDetailVo.getOrderBody()
                                                                                                              .getOrderId()));
        String trackingNo = itemDetailVo.getTrackingNo();
        String carrier = itemDetailVo.getCarrier();
        OrderProductSkuBo productSku = orderItem.getProductSku();
        OrderPageVo orderBody = orderDetailVo.getOrderBody();


        vo.setOrderId(orderBody.getOrderId());


//        switch (orders.getOrderState()){
//            case UnPaid:
//                vo.setOrderStatus("未支付");
//                break;
//            case Paid:
//                vo.setOrderStatus("已支付");
//                break;
//            case Failed:
//                vo.setOrderStatus("支付失败");
//                break;
//            case Canceled:
//                vo.setOrderStatus("已取消");
//                break;
//            case Refunded:
//                vo.setOrderStatus("已退款");
//                break;
//            case Pending:
//                vo.setOrderStatus("待处理");
//                break;
//            case Verifying:
//                vo.setOrderStatus("退款审核中");
//                break;
//            default:
//                vo.setOrderStatus("未知");
//        }
        String orderState = orders.getOrderState().name();
        String fulfillmentType = "";
        if (StrUtil.isNotBlank(orderState)) {
            if (StrUtil.equals(orderState, OrderStateType.UnPaid.name())) {
                vo.setOrderStatus("未支付");
            } else if (StrUtil.equals(orderState, OrderStateType.Paid.name())) {
                vo.setOrderStatus("已支付");
            }else if (StrUtil.equals(orderState, OrderStateType.Refunded.name())) {
                vo.setOrderStatus("已退款");
            } else if (StrUtil.equals(orderState, LogisticsProgress.UnDispatched.name())) {
                vo.setOrderStatus("待发货");
                fulfillmentType = "待发货";
            } else if (StrUtil.equals(orderState, LogisticsProgress.Dispatched.name())) {
                vo.setOrderStatus("已发货");
                fulfillmentType = "已发货";
            } else if (StrUtil.equals(orderState, OrderStateType.Canceled.name())) {
                vo.setOrderStatus("已取消");
            } else if (StrUtil.equals(orderState, LogisticsProgress.Fulfilled.name())) {
                vo.setOrderStatus("已完成");
            } else if (StrUtil.equals(orderState, OrderStateType.Pending.name())) {
                vo.setOrderStatus("已支付");
            }
        }
        String fulfillmentProgress = orders.getFulfillmentProgress().name();
        if(StrUtil.isNotBlank(fulfillmentProgress)){
            if(StrUtil.equals(fulfillmentProgress, LogisticsProgress.UnDispatched.name())){
                fulfillmentType = "待发货";
            }
            if(StrUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched.name())){
                fulfillmentType = "已发货";
            }
            if(StrUtil.equals(fulfillmentProgress, LogisticsProgress.Fulfilled.name())){
                fulfillmentType = "已完成";
            }
        }
        // 发货异常
        if(StringUtils.isNotEmpty(orders.getShipmentException())){
            switch (orders.getShipmentException()){
                case "systemException":
                    vo.setShipmentException("系统异常");
                    break;
                case "produceException":
                    vo.setShipmentException("商品无法关联");
                    break;
                case "addressException":
                    vo.setShipmentException("订单地址异常");
                    break;
                case "updateException":
                    vo.setShipmentException("订单更新异常");
                    break;
                case "pickingException":
                    vo.setShipmentException("订单配货异常");
                    break;
                case "cancelException":
                    vo.setShipmentException("订单取消异常");
                    break;
                case "trackingException":
                    vo.setShipmentException("Tracking异常");
                    break;
                case "costException":
                    vo.setShipmentException("订单成本异常");
                    break;
                case "carrierException":
                    vo.setShipmentException("订单发运方式异常");
                    break;
                case "uploadException":
                    vo.setShipmentException("订单附件上传异常");
                    break;
                case "purchaseException":
                    vo.setShipmentException("订单购买异常");
                    break;
                case "insufficientInventoryException":
                    vo.setShipmentException("库存不足异常");
                    break;
                case "deliverOrderCancelException":
                    vo.setShipmentException("仓库取消单据异常");
                    break;
                case "configException":
                    vo.setShipmentException("配置异常");
                    break;
                case "platformPurchaseException":
                    vo.setShipmentException("订单购买异常-平台时间限制");
                    break;
                default:
                    vo.setShipmentException("无异常");
            }
        }else {
            vo.setShipmentException("无异常");
        }
        vo.setFulfillmentType(fulfillmentType);
        vo.setDistributor(orderBody.getDistributor());
        vo.setDistributorId(orderBody.getDistributorId());
        vo.setStartTime(orderBody.getStartTime());
        vo.setSupplierIds(orderBody.getSupplierIds());

        vo.setProductName(orderItem.getProductName());
        vo.setNum(orderItem.getNum());
        vo.setUnitPrice(orderItem.getUnitPrice());
        vo.setItemNo(productSku.getItemNo());
        vo.setSKU(productSku.getSku());
        vo.setChannelOrderId(orderBody.getChannelOrderId());
        vo.setChannelAlias(orderBody.getChannelAlias());
        vo.setChannelType(orderBody.getChannelType());
        vo.setTotalAmount(orderBody.getTotalNumber());
        vo.setProductAmount(orderBody.getProductAmount());
        vo.setOperationFee(orderBody.getOperationFee());
        vo.setFinalDeliveryFee(orderBody.getFinalDeliveryFee());
        vo.setTotalDeposit(orderBody.getTotalDeposit());
        vo.setLogisticsType(orderBody.getLogisticsType());
        // 此处要按照顺序拼接

        StringBuilder shipTo = getShipTo(orderBody.getShipTo(),AddressFieldEnum.name,AddressFieldEnum.intactAddress,AddressFieldEnum.zip,AddressFieldEnum.contactInformation);

        vo.setShipTo(shipTo.toString());
        vo.setLogisticsAccount(orderBody.getLogisticsAccount());
        vo.setLogisticsAccountZipCode(orderBody.getLogisticsAccountZipCode());
        vo.setNotes(orderBody.getNotes());
        vo.setTotal(orderBody.getTotal());
        //如果carrier或者trackingNo不为空,则拼接;如果为空替换成空格
        if(StrUtil.isBlank(carrier)){
            carrier = " ";
        }
        if(StrUtil.isBlank(trackingNo)){
            trackingNo = " ";
        }
        vo.setTrackingSheet(carrier+" "+trackingNo);
    }

    private List<OrderPageVo> queryPageListForExport(OrdersPageBo dto) {
        log.info("调用【查询订单列表】接口");
        log.info("查询订单列表接口请求参数：{} ", JSONUtil.toJsonStr(dto));

        if (StrUtil.isNotBlank(dto.getQueryType()) && StrUtil.isNotBlank(dto.getQueryValue())) {
            String value = dto.getQueryValue();
            switch (dto.getQueryType()) {
                case "OrderNo":
                    dto.setOrderNo(value);
                    break;
                case "ChannelOrderName":
                    dto.setChannelOrderNo(value);
                    break;
                case "Sku":
                    dto.setSku(value);
                    break;
                case "ProductName":
                    dto.setProductName(value);
                    break;
                case "ProductSkuCode":
                    dto.setItemNo(value);
                    break;
                case "TrackingNo":
                    dto.setTrackingNo(value);
                    break;
                case "ActivityID":
                    dto.setActivityCode(value);
                    break;
                default:
                    break;
            }
        }

        String startDate = null;
        String endDate = null;
        if (StrUtil.isNotBlank(dto.getStartDate())) {
            startDate = dto.getStartDate();
        }
        if (StrUtil.isNotBlank(dto.getEndDate())) {
            endDate = dto.getEndDate();
        }
        String orderState = dto.getOrderState();
        String startTimeGap = dto.getStartTimeGap();

        //查询活动类型
        String activityType = StrUtil.isBlank(dto.getActivityType()) ? null : dto.getActivityType();
        // 创建查询时间的是起始时间和结束时间
        if (StrUtil.isNotBlank(startTimeGap)) {
            DateTime dateTime = new DateTime();
            endDate = DateUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss");
            for (ShipDateEnum shipDateEnum : ShipDateEnum.values()) {
                if (StrUtil.equals(startTimeGap, ShipDateEnum.OneMonth.name())) {
                    startDate = DateUtil.format(DateUtil.offsetMonth(dateTime, -shipDateEnum.getOffset()), "yyyy-MM-dd HH:mm:ss");
                } else if (StrUtil.equals(startTimeGap, shipDateEnum.name())) {
                    startDate = DateUtil.format(DateUtil.offsetDay(dateTime, -shipDateEnum.getOffset()), "yyyy-MM-dd HH:mm:ss");
                }
            }
            log.info("findOrderList - startDateTime = {}", startDate);
            log.info("findOrderList - endDateTime = {}", endDate);
        }

        //订单状态
        List<String> orderStateTypes = new ArrayList<>();
        String fulfillmentType = "";
        if (StrUtil.isNotBlank(orderState)) {
            if (StrUtil.equals(orderState, OrderStateType.UnPaid.name())) {
                orderStateTypes.add(OrderStateType.UnPaid.name());
                orderStateTypes.add(OrderStateType.Failed.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Refunded.name())) {
                orderStateTypes.add(OrderStateType.Refunded.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.UnDispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.UnDispatched.name();
            } else if (StrUtil.equals(orderState, LogisticsProgress.Dispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.Dispatched.name();
            } else if (StrUtil.equals(orderState, OrderStateType.Canceled.name())) {
                orderStateTypes.add(OrderStateType.Canceled.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.Fulfilled.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.Fulfilled.name();
            }
        }

        Integer limit = exportLimit;
        Integer page = 1;

        String sortValue = dto.getSortValue();
        Page<Orders> ordersVoPage = new Page<>(page, limit);
        OrderItem item;
        switch (sortValue) {
            case "createTimeAsc":
                item = OrderItem.asc("create_time");
                break;
            case "orderNoAsc":
                item = OrderItem.asc("order_no");
                break;
            case "orderNoDesc":
                item = OrderItem.desc("order_no");
                break;
            case "priceAsc":
                item = LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)
                    ? OrderItem.asc("platform_payable_total_amount")
                    : OrderItem.asc("original_payable_total_amount");
                break;
            case "priceDesc":
                item = LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)
                    ? OrderItem.desc("platform_payable_total_amount")
                    : OrderItem.desc("original_payable_total_amount");
                break;
            default:
                item = OrderItem.desc("create_time");
                break;

        }
        ordersVoPage.addOrder(item);
        // 入参
        dto.setOrderStates(orderStateTypes);
        dto.setFulfillmentType(fulfillmentType);
        dto.setTenantType(LoginHelper.getTenantType());
        dto.setTenantId(LoginHelper.getTenantId());
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);

        Page<Orders> orderPage = iOrdersService.queryPageList(dto, ordersVoPage);
        List<Orders> records = orderPage.getRecords();
        List<OrderPageVo> orderBodies = new ArrayList<>();
        for (Orders orders : records) {
            Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                       filter(oif -> oif.isThisImpl(orders.getOrderType()))
                                                                                       .findFirst();
            OrderPageVo orderPageVo;
            if (orderHandleInterface.isPresent()) {
                orderPageVo = orderHandleInterface.get().buildOrderBody(orders, activityType);
            } else {
                orderPageVo = orderHandleInterfaces.stream().
                                                   filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst().get()
                                                   .buildOrderBody(orders, activityType);
            }
            orderBodies.add(orderPageVo);
        }
        return orderBodies;

    }

    // 抽到导出工具类里
    private StringBuilder getShipTo(IntactAddressInfoVo shipTo, AddressFieldEnum... enums) {

        StringBuilder sbValue = new StringBuilder();
        String[] fieldNames = getFiledName(shipTo);
        List<String> fields = Arrays.stream(enums).map(AddressFieldEnum::getFieldName).collect(Collectors.toList());


        for (int j = 0; j < fieldNames.length; j++) {     //遍历所有属性
            String name = fieldNames[j];    //获取属性的名字
            Object value = getFieldValueByName(name, shipTo);
            // 写一个指定的属性名称

            if (ObjectUtil.isNotEmpty(value) && fields.contains(String.valueOf(name))) {
//                sbName.append(name);
                sbValue.append(value);
                if (j != fieldNames.length - 1) {

                    sbValue.append(",");
                }
            }
        }
        return sbValue;
    }

    /**
     * 获取属性名数组
     */
    private static String[] getFiledName(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        String[] fieldNames = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
//            System.out.println(fields[i].getType());
            fieldNames[i] = fields[i].getName();
        }
        return fieldNames;
    }

    /* 根据属性名获取属性值
     * */
    private static Object getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[]{});
            Object value = method.invoke(o, new Object[]{});
            return value;
        } catch (Exception e) {

            return null;
        }
    }

    @Override
    public List<OrderPageVo> getOrdersByOrderNosAndTenantId(Collection<String> orderNos, String tenantId) {
        List<OrderPageVo> orderPageVos = new ArrayList<>();
        AtomicReference<List<Orders>> orderPageVoList = new AtomicReference<>();
        TenantHelper.ignore(() -> {
            //查询所有的订单
            orderPageVoList.set(ordersMapper.selectOrderDetailByOrderNos(orderNos, tenantId));
            orderPageVoList.get().forEach(orders -> {
                Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                           filter(oif -> oif.isThisImpl(orders.getOrderType()))
                                                                                           .findFirst();
                OrderPageVo orderPageVo;
                if (orderHandleInterface.isPresent()) {
                    orderPageVo = orderHandleInterface.get().buildOrderBody(orders, null);
                } else {
                    orderPageVo = orderHandleInterfaces.stream().
                                                       filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst().get()
                                                       .buildOrderBody(orders, null);
                }
                orderPageVos.add(orderPageVo);
            });
        });
        return orderPageVos;
    }

    @Override
    public R exportNew(OrdersPageBo orderExportQueryDTO, HttpServletResponse response) {
        String tenantType = LoginHelper.getTenantType();
        if (StrUtil.isBlank(tenantType)) {
            return R.fail("请先登录");
        }
        orderExportQueryDTO=getOrdersPageBo(orderExportQueryDTO);
        String fileName = StrUtil.format(FileNameConstants.ORDER_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        Locale headerLocale = ServletUtils.getHeaderLocale();
        OrdersPageBo finalOrderExportQueryDTO = orderExportQueryDTO;
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.OrdersExport, tempFileSavePath -> {
            try {
                TimeInterval timer = DateUtil.timer();
                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                List<OrderExportListDTO> list = new ArrayList<>();
                // 获取数据总量
                Integer rowCount =  TenantHelper.ignore(()->iOrdersService.countByOrderListVo(finalOrderExportQueryDTO));
                if (rowCount != 0) {
                    //开始创建临时表
                    String temporaryTable="order_export"+ System.currentTimeMillis();
                    temporaryTableMapper.createTemporaryTable(temporaryTable);
                    // 一次查询的数据量
                    int pageSize = 5000;
                    // 总页数
                    int totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);
                    ExcelWriter excelWriter = EasyExcel.write(outputStream, OrderExportListDTO.class).build();
                    for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
                        // 计算当前页的起始行
                        int startRow = (currentPage - 1) * pageSize;
                        // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
                        if (currentPage == totalPage && rowCount % pageSize != 0) {
                            pageSize = rowCount % pageSize;
                        }
                        int finalPageSize = pageSize;
                        try {
                            writeOrderExcel(startRow, finalPageSize, currentPage, excelWriter, finalOrderExportQueryDTO, tenantType,temporaryTable);
                        }catch (Exception e){
                            log.error(StrUtil.format(StrUtil.format("[订单导出],处理数据异常，原因：", e)));
                            throw new RuntimeException(e);
                        }finally {
                            //处理完之后，清空中间表
                            temporaryTableMapper.cleanTemporaryTable(temporaryTable);
                        }
                    }
                    //处理完之后，删除中间表
                    temporaryTableMapper.dropTemporaryTable(temporaryTable);
                    excelWriter.finish();
                } else {
                    ExcelUtil.exportExcelWithLocale(list, "订单导出", OrderExportListDTO.class, false, outputStream, headerLocale);
                }
                IoUtil.close(outputStream);
                Console.log("[订单导出]耗时: {} ms", timer.intervalMs());
                return tempFile;
            } catch (Exception e) {
                log.error(StrUtil.format(StrUtil.format("[订单导出],处理数据异常，原因：", e)));
                throw new RuntimeException(e);
            }
        });
        return R.ok();
    }


 public void writeOrderExcel(int page, int pageSize, int currentPage, ExcelWriter excelWriter,
                             OrdersPageBo orderExportQueryDTO, String tenantType, String temporaryTable) {
        try {
            //分页查询数据
            TimeInterval timer = DateUtil.timer();
            List<OrderExportListDTO> exportListDTO = TenantHelper.ignore(() -> ordersMapper.getOrderExportList(orderExportQueryDTO, page, pageSize), TenantType.Supplier, TenantType.Manager);
            Console.log("[订单导出列表查询]数据库查询数据耗时: {} ms", timer.intervalMs());
//            exportListDTO= exportListDTO.stream().filter(s->s.getOrderNo().equals("Z150049240530501")).collect(Collectors.toList());
//            if (exportListDTO.isEmpty()){
//                return;
//            }
            //存在映射关系的订单
//            List<OrderExportListDTO> mapperOrders = exportListDTO.stream()
//                                                                 .filter(order -> order.getExceptionCode() == null || order.getExceptionCode() != 1)
//                                                                 .collect(Collectors.toList());
            //如果存在关联映射的订单
            if (CollUtil.isNotEmpty(exportListDTO)){
                //处理临时表
                dealOrdersTemporaryTables(exportListDTO,currentPage,temporaryTable);
                // 查询订单关联地址信息
                ConcurrentMap<Long, OrderExportListDTO> temporaryOrderAddressAndLogisticsMap = getTemporaryOrderAddressAndLogisticsMap(temporaryTable);
                // 查询订单关联物流跟踪信息
                ConcurrentMap<String, OrderExportListDTO> temporaryTrackingRecordMap = getTemporaryTrackingRecordMap(temporaryTable);
                //查询产品相关信息
                ConcurrentMap<String, OrderExportListDTO> temporarySkuNameMap = getTemporarySkuNameMap(temporaryTable);
                ConcurrentMap<String, OrderItemPrice> orderItemPriceMap = getOrderItemPriceMap(temporaryTable);
                //查询店铺相关信息
                Map<Long, TenantSalesChannel> tenantSalesChannelMap = getchannelMap(temporaryTable);
                //重新订单发货仓库信息
                ConcurrentMap<String, OrderExportListDTO> warehouseCodeMap = getTemporaryWarehouseCodeMap(temporaryTable);
                // 赋值
                exportListDTO.forEach(s->{
                    //   log.info("订单号"+s.getOrderNo());
//                    if(ObjectUtil.isNotEmpty(s.getLatestDeliveryTime())){
//                        String format = DateUtil.format(DateUtil.parseDate(s.getLatestDeliveryTime()), "yyyy-MM-dd HH:mm:ss");
//                        s.setLatestDeliveryTime(format);
//                    }

                    //地址和物流信息
                    OrderExportListDTO orderAddressAndLogisticsMap = temporaryOrderAddressAndLogisticsMap.get(s.getId());
                    if (orderAddressAndLogisticsMap != null) {
                        s.setAddress(orderAddressAndLogisticsMap.getAddress());
                        s.setRecipient(orderAddressAndLogisticsMap.getRecipient());
                        s.setZipCode(orderAddressAndLogisticsMap.getZipCode());
                        s.setPhoneNumber(orderAddressAndLogisticsMap.getPhoneNumber());
                        s.setCity(orderAddressAndLogisticsMap.getCity());
                        s.setCountry(orderAddressAndLogisticsMap.getCountry());
                        s.setState(orderAddressAndLogisticsMap.getState());
                    }
                    //物流跟踪单
                    if (s.getOrderNo() != null) {
                        OrderExportListDTO trackingRecord = temporaryTrackingRecordMap.get(s.getOrderNo());
                        if (ObjectUtil.isNotNull(trackingRecord)){
                            s.setLogisticsCarrier(trackingRecord.getLogisticsCarrier());
                            s.setLogisticsTrackingNo(trackingRecord.getLogisticsTrackingNo());
                            s.setDispatchedTime(trackingRecord.getDispatchedTime());
                        }
                    }
                    //订单发货仓库
                    if (ObjectUtil.isNotNull(s.getOrderNo())){
                        OrderExportListDTO warehouseMap = warehouseCodeMap.get(s.getOrderNo());
                        if (ObjectUtil.isNotNull(warehouseMap)){
                            s.setLogisticsWarehouse(warehouseMap.getLogisticsWarehouse());
                        }
                    }
                    //产品名称
                    if (s.getProductSkuCode() !=null){
                        OrderExportListDTO productSku= temporarySkuNameMap.get(s.getProductSkuCode());
                        if (ObjectUtil.isNotNull(productSku)){
                            s.setName(productSku.getName());
                            s.setErpSku(productSku.getErpSku());
                        }
                    }
                    //渠道名称信息
                    if (ObjectUtil.isNotEmpty(s.getChannelID())){
                        TenantSalesChannel tenantSalesChannel = tenantSalesChannelMap.get(s.getChannelID());
                        if (ObjectUtil.isNotEmpty(tenantSalesChannel)){
                            s.setChannelName(tenantSalesChannel.getChannelName());
                            s.setChannelType(tenantSalesChannel.getChannelType());
                        }
                    }else {
                        s.setChannelName(s.getChannelAlias());
                        s.setChannelType(s.getChannelType());
                    }
                    OrderItemPrice orderItemPrice = orderItemPriceMap.get(s.getOrderNo());
                    //处理订单金额信息
                    setOrderAmount(s, tenantType, orderItemPrice);
                    // 发货异常
                    if(StringUtils.isNotEmpty(s.getShipmentException())){
                        switch (s.getShipmentException()){
                            case "systemException":
                                s.setShipmentException("系统异常");
                                break;
                            case "produceException":
                                s.setShipmentException("商品无法关联");
                                break;
                            case "addressException":
                                s.setShipmentException("订单地址异常");
                                break;
                            case "updateException":
                                s.setShipmentException("订单更新异常");
                                break;
                            case "pickingException":
                                s.setShipmentException("订单配货异常");
                                break;
                            case "cancelException":
                                s.setShipmentException("订单取消异常");
                                break;
                            case "trackingException":
                                s.setShipmentException("Tracking异常");
                                break;
                            case "costException":
                                s.setShipmentException("订单成本异常");
                                break;
                            case "carrierException":
                                s.setShipmentException("订单发运方式异常");
                                break;
                            case "uploadException":
                                s.setShipmentException("订单附件上传异常");
                                break;
                            case "purchaseException":
                                s.setShipmentException("订单购买异常");
                                break;
                            case "insufficientInventoryException":
                                s.setShipmentException("库存不足异常");
                                break;
                            case "deliverOrderCancelException":
                                s.setShipmentException("仓库取消单据异常");
                                break;
                            case "configException":
                                s.setShipmentException("配置异常");
                                break;
                            case "platformPurchaseException":
                                s.setShipmentException("订单购买异常-平台时间限制");
                                break;
                            default:
                                s.setShipmentException("无异常");
                        }
                    }else {
                        s.setShipmentException("无异常");
                    }
                });
            }
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "订单导出")
                                             // 这里放入动态头
                                             .head(OrderExportListDTO.class)
                                             //传入样式
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             // 当然这里数据也可以用 List<List<String>> 去传入
                                             .build();
            List<String> excludeColumnFieldNames = new ArrayList<>();
            //不是分销商导出的表格中 隐藏"渠道店铺名" ,"渠道SKU"字段
            if (Objects.equals(tenantType, TenantType.Distributor.name())) {
                //分销商
                excludeColumnFieldNames.add("erpSku");
                //分销商ID
                excludeColumnFieldNames.add("tenantId");
            } else if (ObjectUtil.equal(tenantType,TenantType.Supplier.name())){
                //供应商
                //供应商ID
                excludeColumnFieldNames.add("supplierTenantId");
                excludeColumnFieldNames.add("channelSku");
                excludeColumnFieldNames.add("channelName");
            }else {
                excludeColumnFieldNames.add("channelSku");
                excludeColumnFieldNames.add("channelName");
            }
            //不是超管导出订单，隐藏支付时间字段
//            if (!ObjectUtil.equals(tenantType,TenantType.Manager.name())){
//                excludeColumnFieldNames.add("createTime");
//            }
            writeSheet.setExcludeColumnFieldNames(excludeColumnFieldNames);
            excelWriter.write(exportListDTO, writeSheet);
        } catch (Exception e) {
            log.error(Arrays.toString(e.getStackTrace()));
            throw new RuntimeException(e);
        }

    }

    private ConcurrentMap<String, OrderItemPrice> getOrderItemPriceMap(String temporaryTable) {
        List<OrderItemPrice> ignore = TenantHelper.ignore(() -> ordersMapper.selectOrderItemPrice(temporaryTable));
        return ignore.stream().collect(Collectors.toConcurrentMap(OrderItemPrice::getOrderItemNo, Function.identity()));
    }


    /**
     * 处理订单临时表信息
     */
    public  void  dealOrdersTemporaryTables(List<OrderExportListDTO>  exportListDTO,Integer currentPage,String temporaryTableName){
        List<TemporaryTable> temporaryTables = new ArrayList<>();
        StringBuilder sql = new StringBuilder("INSERT INTO "+temporaryTableName+" (value1, value2,value3,value4) VALUES ");
        for (OrderExportListDTO s : exportListDTO) {
            sql.append(String.format("(%d,%d,'%s','%s'),", s.getId(), s.getOrderItemId(),s.getOrderNo(),s.getProductSkuCode()));
        }
        sql.deleteCharAt(sql.length() - 1);
        temporaryTableMapper.insertBySql(sql.toString());

//            //封装临时表
//            exportListDTO.forEach(s -> {
//                TemporaryTable temporaryTable = new TemporaryTable();
//                temporaryTable.setValue1(s.getOrderId());
//                temporaryTable.setValue2(s.getOrderItemId());
//                temporaryTable.setValue3(s.getOrderNo());
//                temporaryTable.setValue4(s.getProductSkuCode());
//                temporaryTables.add(temporaryTable);
//            });
//            temporaryTableMapper.batchInsertTemporaryTable(temporaryTables,temporaryTableName);


    }

    /**
     * @description: 查询订单关联地址信息
     * @author: Len
     * @date: 2024/9/14 13:52
     * @param: temporaryTableName  临时表名称
     * @return: java.util.concurrent.ConcurrentMap<java.lang.Long,com.zsmall.order.entity.domain.bo.order.OrderExportListDTO>
     **/
    public  ConcurrentMap<Long, OrderExportListDTO> getTemporaryOrderAddressAndLogisticsMap(String temporaryTableName){
        //查询订单关联地址信息
        List<OrderExportListDTO> temporaryTableOrderAddressAndLogisticsInfo = TenantHelper.ignore(() -> ordersMapper.selectOrderAddressAndLogisticsInfo(temporaryTableName), TenantType.Supplier, TenantType.Manager);
        //log.info("temporaryTableAddressInfo:{}", JSON.toJSONString(temporaryTableOrderAddressAndLogisticsInfo));
        //处理地址信息
        return temporaryTableOrderAddressAndLogisticsInfo.stream().collect(Collectors.toConcurrentMap(OrderExportListDTO::getId, Function.identity()));
    }


    /**
     * @description: 查询订单关联商品信息
     * @author: Len
     * @date: 2024/9/14 13:52
     * @param: temporaryTableName 临时表名称
     * @return: java.util.concurrent.ConcurrentMap<java.lang.String,com.zsmall.order.entity.domain.bo.order.OrderExportListDTO>
     **/
    public  ConcurrentMap<String, OrderExportListDTO> getTemporarySkuNameMap(String temporaryTableName){
        //查询订单关联商品信息
        List<OrderExportListDTO> temporarySkuName = TenantHelper.ignore(() -> ordersMapper.selectOrderSkuName(temporaryTableName), TenantType.Supplier, TenantType.Manager);
        //log.info("temporarySkuName:{}", JSON.toJSONString(temporarySkuName));
        //处理产品名称
        return temporarySkuName.stream().collect(Collectors.toConcurrentMap(OrderExportListDTO::getProductSkuCode, Function.identity()));
    }


    /**
     * @description: 查询渠道
     * @author: Len
     * @date: 2024/9/14 13:51
     * @param: temporaryTable 中间表名称
     * @return: java.util.Map<java.lang.Long,com.zsmall.system.entity.domain.TenantSalesChannel>
     **/
    public  Map<Long, TenantSalesChannel>  getchannelMap(String temporaryTable){
        //获取渠道名称信息
        List<TenantSalesChannel> tenantSalesChannelList=TenantHelper.ignore(()-> iTenantSalesChannelService.list());
         return tenantSalesChannelList.stream().collect(Collectors.toMap(TenantSalesChannel::getId, tenantSalesChannel -> tenantSalesChannel));
    }


    /**
     * @description: 查询订单关联物流跟踪信息
     * @author: Len
     * @date: 2024/9/14 13:52
     * @param: temporaryTableName 临时表名称
     * @return: java.util.concurrent.ConcurrentMap<java.lang.String,com.zsmall.order.entity.domain.bo.order.OrderExportListDTO>
     **/
    public  ConcurrentMap<String, OrderExportListDTO>  getTemporaryTrackingRecordMap(String temporaryTableName){
        //查询订单关联子订单物流跟踪信息
        List<OrderExportListDTO> temporaryTrackingRecord = TenantHelper.ignore(() -> ordersMapper.selectOrderTrackingRecord(temporaryTableName), TenantType.Supplier, TenantType.Manager);
        //log.info("temporaryTrackingRecord:{}", JSON.toJSONString(temporaryTrackingRecord));
        //处理物流单号
        return temporaryTrackingRecord.stream().collect(Collectors.toConcurrentMap(
              // key为orderNo
              OrderExportListDTO::getOrderNo,
              // value为OrderExportListDTO对象本身
              Function.identity(),
              (existingValue, newValue) -> {
                  // 如果key重复，则拼接logistics
                  existingValue.setLogisticsTrackingNo(existingValue.getLogisticsTrackingNo() + "," + newValue.getLogisticsTrackingNo());
                  return existingValue;
              }
          ));
    }

    /**
     * @description: 查询订单关联发货仓信息
     * @author: Len
     * @date: 2024/9/14 13:52
     * @param: temporaryTableName 临时表名称
     * @return: java.util.concurrent.ConcurrentMap<java.lang.String,com.zsmall.order.entity.domain.bo.order.OrderExportListDTO>
     **/
    private ConcurrentMap<String, OrderExportListDTO> getTemporaryWarehouseCodeMap(String temporaryTable) {
        List<OrderExportListDTO> temporaryWarehouseCode = TenantHelper.ignore(() -> ordersMapper.selectOrderWarehouseCode(temporaryTable), TenantType.Supplier, TenantType.Manager);
        //log.info("temporaryTrackingRecord:{}", JSON.toJSONString(temporaryTrackingRecord));
        //处理物流单号
        return temporaryWarehouseCode.stream()
                                     .collect(Collectors.toConcurrentMap(
                                         OrderExportListDTO::getOrderNo, // keyMapper
                                         Function.identity(),            // valueMapper
                                         (existing, replacement) -> replacement // mergeFunction，当键冲突时保留后面的值
                                     ));

    }


    /**
     * 设置订单金额信息
     * @param exportListDTO 导出的订单列表
     * @param tenantType 租户类型
     */
    public  void  setOrderAmount(OrderExportListDTO exportListDTO,String tenantType,OrderItemPrice orderItemPrice) {
        //   需付款/实付金额：total   销售额金额：totalAmount  应付金额：productAmount
        //   操作费：operationFee 运费：shippingCost 尾程派送费：finalDeliveryFee 定金： totalDeposit
        //供应商
        if (Objects.equals(tenantType, TenantType.Supplier.name())) {
            //销售额金额：
            exportListDTO.setTotalAmount(exportListDTO.getOriginalActualTotalAmount());
            //如果异常码为空，同时异常码不等于1
            if (ObjectUtil.isNull(exportListDTO.getExceptionCode()) || exportListDTO.getExceptionCode() != 1) {
                //原始应付总金额（供货商）
                exportListDTO.setProductAmount(exportListDTO.getOriginalPayableTotalAmount());
                //单价
                if (ObjectUtil.isNotNull(orderItemPrice)){
                    exportListDTO.setProductSkuUnitPrice(String.valueOf(orderItemPrice.getOriginalUnitPrice()));
                }
//                if (ObjectUtil.isNotNull(exportListDTO.getOriginalActualTotalAmount()) && !"0".equals(exportListDTO.getQuantity())) {
//                    exportListDTO.setPrice(String.valueOf(BigDecimal.valueOf(Double.parseDouble(exportListDTO.getOriginalActualTotalAmount()))
//                                                                    .divide(BigDecimal.valueOf(Double.parseDouble(exportListDTO.getQuantity())), 2, RoundingMode.HALF_UP)));
//                }
                //    log.error(StrUtil.format("订单编码{}金额{}，数量{}",exportListDTO.getOrderNo(),exportListDTO.getOriginalPayableTotalAmount(),exportListDTO.getQuantity()));
                //原始操作费总金额（供货商）
                exportListDTO.setOperationFee(exportListDTO.getOriginalTotalOperationFee());
                //原始尾程派送费总额
                exportListDTO.setFinalDeliveryFee(exportListDTO.getOriginalTotalFinalDeliveryFee());
                //实付金额：
                exportListDTO.setTotal(exportListDTO.getOriginalActualTotalAmount());
                // 小计
                if (ObjectUtil.equal(LogisticsTypeEnum.PickUp.name(),exportListDTO.getLogisticsType())){
                    exportListDTO.setCommoditySubtotal(String.valueOf(exportListDTO.getOriginalTotalPickUpPrice()));
                }else {
                    exportListDTO.setCommoditySubtotal(String.valueOf(exportListDTO.getOriginalTotalDropShippingPrice()));
                }
                //定金
             //   exportListDTO.setTotalDeposit(exportListDTO.getOriginalPrepaidTotalAmount());
            }
        } else {
            //销售额金额：
            exportListDTO.setTotalAmount(exportListDTO.getPlatformActualTotalAmount());
            if (ObjectUtil.isNull(exportListDTO.getExceptionCode()) || exportListDTO.getExceptionCode() != 1) {
                //实付金额：
                exportListDTO.setTotal(exportListDTO.getOriginalActualTotalAmount());
                //应付金额
                //  orderPageVo.setProductAmount(String.valueOf(orders.getOriginalActualTotalAmount()));
                exportListDTO.setProductAmount(exportListDTO.getPlatformPayableTotalAmount());
                //单价
                if (ObjectUtil.isNotNull(orderItemPrice)){
                    exportListDTO.setProductSkuUnitPrice(String.valueOf(orderItemPrice.getPlatformUnitPrice()));
                    }
//                if (ObjectUtil.isNotNull(exportListDTO.getPlatformActualTotalAmount()) && !"0".equals(exportListDTO.getQuantity())) {
//                    exportListDTO.setPrice(String.valueOf(BigDecimal.valueOf(Double.parseDouble(exportListDTO.getPlatformActualTotalAmount()))
//                                                                        .divide(BigDecimal.valueOf(Double.parseDouble(exportListDTO.getQuantity())), 2, RoundingMode.HALF_UP)));
//                }
                    //   log.error(StrUtil.format("订单编码{}，金额{}，数量{}",exportListDTO.getOrderNo(),exportListDTO.getOriginalActualTotalAmount(),exportListDTO.getQuantity()));
                    //原始操作费总金额（供货商）
                    exportListDTO.setOperationFee(exportListDTO.getPlatformTotalOperationFee());
                    //原始尾程派送费总额
                    exportListDTO.setFinalDeliveryFee(exportListDTO.getPlatformTotalFinalDeliveryFee());
                    //定金
                //   exportListDTO.setTotalDeposit(exportListDTO.getPlatformPrepaidTotalAmount());
                // 小计
                if (ObjectUtil.equal(LogisticsTypeEnum.PickUp.name(),exportListDTO.getLogisticsType())){
                    exportListDTO.setCommoditySubtotal(String.valueOf(exportListDTO.getOriginalTotalPickUpPrice()));
                }else {
                    exportListDTO.setCommoditySubtotal(String.valueOf(exportListDTO.getOriginalTotalDropShippingPrice()));
                }
            }
        }
    }

    @Override
    public void batchTaggingOrders(List<String> orderNos) {
        if (CollectionUtil.isEmpty(orderNos)){
            return;
        }
        //查询订单是否存在 和订单状态是否是待支付
        LambdaQueryWrapper<Orders> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CollectionUtil.isNotEmpty(orderNos),Orders::getOrderNo,orderNos);
        lambdaQueryWrapper.eq(Orders::getOrderState,OrderStateType.UnPaid);
        List<Orders> orders = ordersMapper.selectList(lambdaQueryWrapper);
        //获取差集
        // 将List<Orders>转换为List<String>，只包含orderNo字段
        List<String> orderNosFromOrders = orders.stream()
                                                    .map(Orders::getOrderNo)
                                                    .collect(Collectors.toList());

        // 找出差集
        List<String> difference = orderNos.stream()
                                          .filter(orderNo -> !orderNosFromOrders.contains(orderNo))
                                          .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(difference)){
            throw  new RuntimeException("订单编号"+difference+"不存在或订单状态不是待支付！");
        }
        //更新数据库
         orders.forEach(s->{
             s.setUpdateTime(new Date());
             s.setIsShow(2);
         });
        ordersMapper.updateBatchById(orders);
    }

    @Override
    public void restoreOrders(List<String> orderNo) {
        UpdateWrapper<Orders> wa =new UpdateWrapper();
        wa.in("order_no",orderNo);
        wa.set("is_show",null);
        ordersMapper.update(null,wa);
    }

    @Override
    public R<Void> payOrderForV4(OrderPayBo bo) throws OrderPayException, StockException, ProductException {
        String tenantId = bo.getTenantId();
        List<String> orderNoList = bo.getOrderNoList();
        if (CollUtil.isEmpty(orderNoList)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        List<Orders> orderList = iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid, OrderStateType.Failed);
        for (Orders orders : orderList) {
            try {
                ArrayList<Orders> list = new ArrayList<>();
                list.add(orders);
                if (CollUtil.size(orderNoList) == CollUtil.size(orderNoList)) {
                    orderSupport.orderPayChainNotToErpV3(tenantId, list, true, true);
                } else {
                    throw new RStatusCodeException(OrderStatusCodeEnum.ORDER_STATE_HAS_CHANGED);
                }
            } catch (Exception e) {
                log.error("订单支付失败:", e);
            }
        }

        return R.ok(ZSMallStatusCodeEnum.PAY_ORDER_SUCCESS);
    }

    @Override
    public void updateOrSetNull(Orders orders) {
        if(ObjectUtil.isNotEmpty(orders)&&(
            OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(orders.getExceptionCode()))
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(orders.getExceptionCode())
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(orders.getExceptionCode())){
            Integer orderSource = orders.getOrderSource();
            if(!OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)&&!OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource)){
                iOrdersService.updateBatchToNull(Collections.singletonList(orders.getId()),true);
            }
            if(OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource)){
                iOrdersService.updateBatchToNull(Collections.singletonList(orders.getId()),false);
            }
        }else {
            // 更新订单价格信息
            if (ObjectUtil.isNotEmpty(orders)) {
                iOrdersService.updateNoTenant(orders);
            }
        }
    }

    @Override
    public void updateBatchOrSetNull(List<Orders> updateOrderList, HashMap<String, Integer> codesMap) {

    }

    @Override
    public void updateOrSetNull(List<Orders> orders, Integer orderExceptionCode) {

    }

    /**
     * 功能描述：更新批量补偿
     *
     * @param needCompensateOrders 需要补偿订单
     * @return {@link List }<{@link Orders }>
     * <AUTHOR>
     * @date 2024/08/14
     */
    public List<String> updateBatchCompensate(List<Orders> needCompensateOrders) {
        List<Orders> orders = new ArrayList<>();
        for (Orders needCompensateOrder : needCompensateOrders) {
            // 补偿失败的不需要更新订单,成功的需要更新
            priceBussinessSupport.recalculateAndUpdateForPrice(needCompensateOrder,new ConcurrentHashMap<>());
            // 如果 order 的配送费有值了,就加入到支付逻辑内
            BigDecimal originalTotalDropShippingPrice = needCompensateOrder.getOriginalTotalDropShippingPrice();
            if(ObjectUtil.isNotEmpty(originalTotalDropShippingPrice)){
                // 补偿成功
                log.info("订单号：{} 配送费补偿成功",needCompensateOrder.getOrderNo());
                orders.add(needCompensateOrder);
            }
        }

        for (Orders order : orders) {

            if(ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(order.getTenantId(),order.getCurrency())){
                OrderPayBo bo = new OrderPayBo();

                bo.addOrderNoList(Collections.singletonList(order.getOrderNo()));
                bo.setPaymentPassword(null);
                try {
                    TenantHelper.ignore(() -> {
                        try {
                            payOrderForErp(bo, order.getTenantId(), true, true);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            throw new RuntimeException(e.getMessage());
                        }
                    });
                } catch (Exception e) {
                    log.error("支付失败", e);
                }
            }

        }
//        存在needCompensateOrders内不在orders内的订单号
//        log.info("批量补偿失败的订单号为：{}", JSONObject.toJSONString(failedOrderNos));
        // 查询一下订单状态是否已经改变
        List<String> orderNos = needCompensateOrders.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(orderNos, OrderStateType.Paid);
        List<String> successList = ordersList.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        return successList;
    }

    @Transactional
    @Override
    public void updateOrderWarehouseCode(String orderNo, String warehouseSystemCode) {
        if (!ObjectUtil.equals(LoginHelper.getTenantType(),TenantType.Distributor.name())){
            throw new RuntimeException("非分销商不支持此操作:"+orderNo);
        }
        Orders byOrderNo = iOrdersService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(byOrderNo)){
            throw new RuntimeException("订单不存在:"+orderNo);
        }
        if (!ObjectUtil.equal(LogisticsTypeEnum.PickUp.name(),byOrderNo.getLogisticsType().name()) ){
            throw new RuntimeException("非自提订单不支持修改仓库:"+orderNo);
        }
        if ( !ObjectUtil.equal(OrderStateType.Failed.name(),byOrderNo.getOrderState().name()) ){
            throw new RuntimeException("自提订单非支付失败状态不支持修改仓库:"+orderNo);
        }
//        List<Integer> exceptionValue = List.of(OrderExceptionEnum.out_of_stock_exception.getValue(),
//            OrderExceptionEnum.stock_same_warehouse_code_exists.getValue(),
//            OrderExceptionEnum.Delivery_exception.getValue(),
//            OrderExceptionEnum.warehouse_mapping_exception.getValue());
//
//        if (!exceptionValue.contains(byOrderNo.getExceptionCode()) ){
//            throw new RuntimeException("自提订单支付失败状态,非库存异常/仓库编号异常/发货方式异常/仓库映射异常 不支持修改仓库:"+orderNo);
//        }
        LambdaQueryWrapper<com.zsmall.order.entity.domain.OrderItem> o = new LambdaQueryWrapper<>();
        o.eq(com.zsmall.order.entity.domain.OrderItem::getOrderId,byOrderNo.getId());
        com.zsmall.order.entity.domain.OrderItem orderItem = orderItemService.getOne(o);
        if (ObjectUtil.isNull(orderItem)){
            throw new RuntimeException("订单明细不存在:"+orderNo);
        }
        Warehouse warehouse = warehouseService.queryByWarehouseSystemCode(warehouseSystemCode);
        if (ObjectUtil.isNull(warehouse)){
            throw new RuntimeException("仓库信息不存在:"+warehouseSystemCode);
        }
        //查询仓库编码

        List<WarehouseVo> inStockWarehouseBySku =TenantHelper.ignore(()->productSkuService.getInStockWarehouseBySku(orderItem.getProductSkuCode(), 1,warehouseSystemCode));
        Set<String> warehouseSystemCodes = inStockWarehouseBySku.stream()
                                                        .map(WarehouseVo::getWarehouseSystemCode)
                                                        .collect(Collectors.toSet());
        if (CollUtil.isEmpty(warehouseSystemCodes)){
            throw new RuntimeException(StrUtil.format("订单号:{},所属的商品:{},无库存",orderNo,orderItem.getProductSkuCode()));
        }
        if (!warehouseSystemCodes.contains(warehouseSystemCode)){
            throw new RuntimeException(StrUtil.format("订单号:{},所属的商品:{},当前仓库不可分配:{}",orderNo,orderItem.getProductSkuCode(),warehouseSystemCode));
        }

        //更新仓库相关表的仓库编码
        OrderItemProductSku byOrderItemId = orderItemProductSkuService.getByOrderItemId(orderItem.getId());
        if (ObjectUtil.isNotNull(byOrderItemId)){
            byOrderItemId.setSpecifyWarehouse(warehouse.getWarehouseCode());
            byOrderItemId.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
            orderItemProductSkuService.updateById(byOrderItemId);
        }
        LambdaQueryWrapper<OrderItemTrackingRecord> oo = new LambdaQueryWrapper<>();
        oo.eq(OrderItemTrackingRecord::getOrderItemNo,orderItem.getOrderItemNo());
        List<OrderItemTrackingRecord> orderItemTrackingRecords = orderItemTrackingRecordService.getBaseMapper()
                                                                                               .selectList(oo);
        if (CollUtil.isNotEmpty(orderItemTrackingRecords)){
            orderItemTrackingRecords.forEach(s->{
                s.setWarehouseCode(warehouse.getWarehouseCode());
                s.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                orderItemTrackingRecordService.updateById(s);

            });
        }
        // 重新计算订单价格
        orderSupport.computeOrderPrice(List.of(byOrderNo));
    }

    @Override
    public void getOrderLogisticsAttachment(List<String> orderNoList) {
        if(CollUtil.isEmpty(orderNoList)){
            return;
        }
        List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.UnPaid,OrderStateType.Pending);
        if (CollUtil.isEmpty(ordersList)){
            return;
        }
        orderSupport.sendAmazonVCGetLogisticsAttachmentMessage(ordersList);
    }

    @Override
    public void orderLogisticsAttachmentDeal(AmazonVCOrderLogisticsAttachmentCallBackDTO amazonVCOrderLogisticsAttachmentCallBackDTO) {
        if(ObjectUtil.isNotNull(amazonVCOrderLogisticsAttachmentCallBackDTO) && StringUtils.isNotEmpty(amazonVCOrderLogisticsAttachmentCallBackDTO.getOrderNo()) && StringUtils.isNotEmpty(amazonVCOrderLogisticsAttachmentCallBackDTO.getChannel())){
            TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonVCOrderLogisticsAttachmentCallBackDTO.getChannel());
            if(ObjectUtil.isNull(tenantSalesChannel)){
                log.error("amazonVC订单获取面单接口根据店铺标识获取租户为空，店铺标识: {}",amazonVCOrderLogisticsAttachmentCallBackDTO.getChannel());
                return;
            }
            // 处理面单
            orderSupport.amazonVCOrderAttachmentCallBack(amazonVCOrderLogisticsAttachmentCallBackDTO);
        }
    }

    @Override
    @InMethodLog("设置订单的tracking信息")
    @Transactional(rollbackFor = Exception.class)
    public void setOrderTrackingInfo(SetOrderTrackingInfoBo bo) {
        if(CollUtil.isEmpty(bo.getTrackingNo())){
            throw new RuntimeException("物流单号不能为空");
        }
        String orderNo = bo.getOrderNo();
        Orders orders = iOrdersService.getByOrderNo(orderNo);
        if(null == orders){
            log.error("根据订单号查询的订单为空:{}", orderNo);
            return;
        }
        if(!LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
            throw new RuntimeException("只有自提订单才可填写tracking信息");
        }
        if (ChannelTypeEnum.EC.equals(orders.getChannelType()) || ChannelTypeEnum.Amazon_VC.equals(orders.getChannelType())
             && 1 == orders.getOrderSource()){
            if(CollUtil.isNotEmpty(bo.getTrackingNo()) && bo.getTrackingNo().size()>1){
                throw new RuntimeException("amazonVC订单和EC的接口自提订单一次只能设置一个trackingNO");
            }
        }
        List<com.zsmall.order.entity.domain.OrderItem> listByOrderId = iOrderItemService.getListByOrderId(orders.getId());
        com.zsmall.order.entity.domain.OrderItem orderItem = new com.zsmall.order.entity.domain.OrderItem();
        if(CollUtil.isNotEmpty(listByOrderId)){
             orderItem = listByOrderId.get(0);
        }
        if(ObjectUtil.isNull(orderItem)){
            log.error("设置订单tracking信息,根据订单ID查询不到订单详情信息:{}", orders.getId());
            return;
        }
        String trimTracking = StrUtil.trim(bo.getTrackingNo().get(0));
         OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
        if(ObjectUtil.isNull(orderItemProductSku)){
            log.error("设置订单tracking信息,根据订单ID查询不到订单ProductSku信息:{}", orders.getId());
            return;
        }
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
        if(ObjectUtil.isNull(orderLogisticsInfo)){
            log.error("设置订单tracking信息,根据订单ID查询不到订单LogisticsInfo信息:{}", orders.getId());
            return;
        }
        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNo(orders.getOrderNo());
        if(ObjectUtil.isEmpty(orderAddressInfo)){
            log.error("设置订单tracking信息,根据订单ID查询不到订单AddressInfo信息:{}", orders.getId());
            return;
        }
        if (StrUtil.isNotBlank(trimTracking)) {
            String logisticsCarrier = orderLogisticsInfo.getLogisticsCarrierCode();
            String logisticsCarrierCode = orderLogisticsInfo.getLogisticsCarrierCode();
            if(StringUtils.isEmpty(logisticsCarrierCode)){
                log.error("设置订单tracking信息,订单的物流商为空:{}", logisticsCarrierCode);
                return;
            }
            if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "FED")) {
                logisticsCarrier = "FedEx";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "UPS")) {
                logisticsCarrier = "UPS";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "AMZL")) {
                logisticsCarrier = "AMZL";
            }else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "ONTRAC")) {
                logisticsCarrier = "OnTrac";
            } else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "USPS")) {
                logisticsCarrier = "UPS";
            }else if (StrUtil.contains(logisticsCarrierCode.toUpperCase(), "AMSP")) {
                logisticsCarrier = "AMSP";
            }
            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
            trackingRecord.setSku(orderItemProductSku.getSku());
            trackingRecord.setProductSkuCode(orderItemProductSku.getProductSkuCode());
            trackingRecord.setLogisticsCarrier(logisticsCarrier);
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orders.getOrderNo());
            trackingRecord.setOrderItemNo(orderItem.getOrderItemNo());
            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            ProductSkuStock stock = productSkuStockService.getStockForDeliver(orderAddressInfo.getCountryCode(),orderItemProductSku.getProductSkuCode(), orders.getLogisticsType(),orderItem.getTotalQuantity(),Boolean.FALSE, orderLogisticsInfo.getZipCode(),orderLogisticsInfo.getLogisticsCarrierCode(), orders.getTenantId(), orderItem.getSupplierTenantId());
            if(ObjectUtil.isNotEmpty(stock)){
                trackingRecord.setWarehouseSystemCode(stock.getWarehouseSystemCode());
            }else{
                trackingRecord.setWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
            }
            trackingRecord.setQuantity(orderItem.getTotalQuantity());
            iOrderItemTrackingRecordService.remove(new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderItemNo,orderItem.getOrderItemNo()));
            iOrderItemTrackingRecordService.save(trackingRecord);
        }

    }

    @Override
    public void exportOrderAttachmentByEs(OrdersPageBo dto, HttpServletResponse response) {
        LambdaEsQueryWrapper<EsOrdersDTO> wa = getEsOrdersQueryWrapper(dto);
        wa.orderByDesc(EsOrdersDTO::getId);
        String fileName = StrUtil.format(FileNameConstants.ORDER_ATTACHMENT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.OrderAttachmentExport, tempFileSavePath -> {
            try {
                TimeInterval timer = DateUtil.timer();
                timer.start();
                // 获取数据总量
                int rowCount = Math.toIntExact(esOrderMapper.selectCount(wa));
             //   Integer rowCount= ordersMapper.countOrderAttachment(dto);
                String exportPath = "./面单";
                Path dir = Paths.get(exportPath);
                if (rowCount != 0) {
                    //只处理5000条
                   //  rowCount= 5000;
                    //附件文件目录
                    String rootPath = ossRootPath;
                    //导出缓存目录
                    Files.createDirectories(Paths.get(rootPath));
                    FileUtil.del(dir);
                    Files.createDirectories(dir);
                    String temporaryTable="order_attachment_export"+ System.currentTimeMillis();
                    temporaryTableMapper.createTemporaryTable(temporaryTable);
                    SAPageInfo<EsOrdersDTO> saPageInfo = esOrderMapper.searchAfterPage(wa,  null, 5000);
                    List<EsOrdersDTO> esOrdersDTOS = saPageInfo.getList();
                    if (CollUtil.isNotEmpty(esOrdersDTOS)){
                        StringBuilder sql = new StringBuilder("INSERT INTO "+temporaryTable+" (value3) VALUES ");
                        for (EsOrdersDTO s : esOrdersDTOS) {
                            sql.append(String.format("('%s'),", s.getOrderNo()));
                        }
                        sql.deleteCharAt(sql.length() - 1);
                        temporaryTableMapper.insertBySql(sql.toString());
                    }else {
                        log.error(StrUtil.format(StrUtil.format("[订单导出],查询数据为空，原因：", esOrdersDTOS)));
                        throw new RuntimeException("查询数据为空");
                    }
                    try {
                        dealOrderAttachmentByEs(temporaryTable,rootPath,exportPath);
                    }catch (Exception e){
                        log.error(StrUtil.format(StrUtil.format("[订单导出],处理数据异常，原因：", e)));
                        throw new RuntimeException(e);
                    }finally {
                        //删除中间表
                        temporaryTableMapper.dropTemporaryTable(temporaryTable);
                    }

                }else {
                    FileUtil.del(dir);
                    Files.createDirectories(dir);
                }
                File zip = ZipUtil.zip(dir.toFile());
                Console.log("[订单附件导出]耗时: {} ms", timer.intervalMs());
                return zip;
            } catch (Exception e) {
                log.error(StrUtil.format(StrUtil.format("[订单附件导出],处理数据异常，原因：", e)));
                throw new RuntimeException(e);
            }
        });

    }

    @Override
    public void exportOrderAttachment(OrdersPageBo dto, HttpServletResponse response) {
        log.info("查询订单附件导出接口请求参数：{} ", JSONUtil.toJsonStr(dto));
        List<String>orderNos;
        List<String>channelOrderNames;
        if (StrUtil.isNotBlank(dto.getQueryType()) && StrUtil.isNotBlank(dto.getQueryValue())) {
            String value = dto.getQueryValue();
            switch (dto.getQueryType()) {
                case "OrderNo":
                    // 逗号
                    String[] orderArray = value.split(",");
                    // 将结果存储在 List<String> 中
                    orderNos = new ArrayList<>(Arrays.asList(orderArray));
                    if(CollUtil.isNotEmpty(orderNos)&&orderNos.size()>1){
                        dto.putOrderNos(orderNos);
                    }else {
                        dto.setOrderNo(value);
                    }
                    break;
                case "ChannelOrderName":
                    String[] channelArray = value.split(",");
                    // 将结果存储在 List<String> 中
                    channelOrderNames = new ArrayList<>(Arrays.asList(channelArray));
                    if(CollUtil.isNotEmpty(channelOrderNames)&&channelOrderNames.size()>1){
                        dto.putChannelOrderNos(channelOrderNames);
                    }else {
                        dto.setChannelOrderNo(value);
                    }
                    break;
                case "Sku":
                    dto.setSku(value);
                    break;
                case "ProductName":
                    dto.setProductName(value);
                    break;
                case "ProductSkuCode":
                    dto.setItemNo(value);
                    break;
                case "TrackingNo":
                    dto.setTrackingNo(value);
                    break;
                case "ActivityID":
                    dto.setActivityCode(value);
                    break;
                case "SupplierTenantId":
                    dto.setSupplierTenantId(value);
                    break;
                case "DistributorTenantId":
                    dto.setDistributorTenantId(value);
                    break;
                default:
                    break;
            }
        }
        String orderState = dto.getOrderState();
        //订单状态
        List<String> orderStateTypes = new ArrayList<>();
        String fulfillmentType = "";
        if (StrUtil.isNotBlank(orderState)) {
            if (StrUtil.equals(orderState, OrderStateType.UnPaid.name())) {
                orderStateTypes.add(OrderStateType.UnPaid.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Failed.name())) {
                orderStateTypes.add(OrderStateType.Failed.name());
            } else if (StrUtil.equals(orderState, OrderStateType.Refunded.name())) {
                orderStateTypes.add(OrderStateType.Refunded.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.UnDispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.UnDispatched.name();
            } else if (StrUtil.equals(orderState, LogisticsProgress.Dispatched.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.Dispatched.name();
            } else if (StrUtil.equals(orderState, OrderStateType.Canceled.name())) {
                orderStateTypes.add(OrderStateType.Canceled.name());
            } else if (StrUtil.equals(orderState, LogisticsProgress.Fulfilled.name())) {
                orderStateTypes.add(OrderStateType.Paid.name());
                fulfillmentType = LogisticsProgress.Fulfilled.name();
            } else if (StrUtil.equals(orderState, OrderStateType.Pending.name())) {
                orderStateTypes.add(OrderStateType.Pending.name());
            }
        }
        dto.setSortValue("o.id desc");
        dto.setOrderStates(orderStateTypes);
        dto.setFulfillmentType(fulfillmentType);
        dto.setTenantType(LoginHelper.getTenantType());
        dto.setTenantId(LoginHelper.getTenantId());
        dto.setIsShowOrder(false);

        String fileName = StrUtil.format(FileNameConstants.ORDER_ATTACHMENT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.OrderAttachmentExport, tempFileSavePath -> {
            try {
                TimeInterval timer = DateUtil.timer();
                timer.start();
                // 获取数据总量
                Integer rowCount= ordersMapper.countOrderAttachment(dto);
                String exportPath = "./面单";
                Path dir = Paths.get(exportPath);
                if (rowCount != 0) {
                    //只处理5000条
                     rowCount= 5000;
                    //附件文件目录
                    String rootPath = ossRootPath;
                    //导出缓存目录
                    Files.createDirectories(Paths.get(rootPath));
                    FileUtil.del(dir);
                    Files.createDirectories(dir);
                    // 一次查询的数据量
                    int pageSize = 5000;
                    // 总页数
                    int totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);
                    for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
                        //开始创建临时表
                        String temporaryTable="order_attachment_export"+ System.currentTimeMillis();
                        temporaryTableMapper.createTemporaryTable(temporaryTable);
                        // 计算当前页的起始行
                        int startRow = (currentPage - 1) * pageSize;
                        // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
                        if (currentPage == totalPage && rowCount % pageSize != 0) {
                            pageSize = rowCount % pageSize;
                        }
                        int finalPageSize = pageSize;
                        try {
                            dealOrderAttachment(startRow, finalPageSize, dto,temporaryTable,rootPath,exportPath);
                        }catch (Exception e){
                            log.error(StrUtil.format(StrUtil.format("[订单导出],处理数据异常，原因：", e)));
                            throw new RuntimeException(e);
                        }finally {
                            //删除中间表
                            temporaryTableMapper.dropTemporaryTable(temporaryTable);
                        }
                    }

                }else {
                    FileUtil.del(dir);
                    Files.createDirectories(dir);
                }
                File zip = ZipUtil.zip(dir.toFile());
                Console.log("[订单附件导出]耗时: {} ms", timer.intervalMs());
                return zip;
            } catch (Exception e) {
                log.error(StrUtil.format(StrUtil.format("[订单附件导出],处理数据异常，原因：", e)));
                throw new RuntimeException(e);
            }
        });

    }
    @Override
    public void testSysConfig(String key) {
        boolean notExist = iSysApiService.isNotExist(key);
    }
    @Override
    public Orders setSiteField(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        SiteMsgBo siteBo = orderReceiveFromThirdDTO.getAddress().getSiteBo();

        orders.setCurrency(siteBo.getCurrencyCode());
        orders.setCurrencySymbol(siteBo.getCurrencySymbol());
        orders.setSiteId(siteBo.getSiteId());
        orders.setCountryCode(siteBo.getCountryCode());
        return orders;
    }

    @Override
    public JSONObject getAbnormalServiceMessage(Orders orders, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        SaleOrderItemDTO itemDTO = saleOrderItemsList.get(0);
        Long channelId = orderReceiveFromThirdDTO.getShopId();
        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
        String regionCode = address.getRegionCode();
        SiteCountryCurrency site = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
        ProductMapping mapping = TenantHelper.ignore(()->iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountryV2(orderReceiveFromThirdDTO.getTenantId(), channelId, itemDTO.getSkuId(), SyncStateEnum.Mapped, site.getCountryCode()));
        if(ObjectUtil.isNotEmpty(mapping)&&ObjectUtil.isNotEmpty(mapping.getProductSkuCode())){

            ProductSkuPrice price = iProductSkuPriceService.queryByProductSkuCodeAndSite(mapping.getProductSkuCode(), site.getId());
            if(ObjectUtil.isEmpty(price)){
                LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(mapping.getProductSkuCode()));

                return localeMessage.toJSON();
            }
        }
        return null;
    }

    @Override
    public void batchSetTracking(MultipartFile file) throws IOException {
        String name = file.getOriginalFilename();
        InputStream inputStream = file.getInputStream();
        DataSource dataSource = DS.determineDataSource();
        LocaleMessage localeMessage = new LocaleMessage();
        List<OrderItemTrackingVO> orderItemTrackingRecords = new ArrayList<>();
        Set<String> orderNos = new HashSet<>();
        Map<String,List<OutShipment>> outShipmentMap=new HashMap<>();
        Boolean uploadResult = Boolean.FALSE;
        OrderItemTrackingListener listener = listenerFactory.createListener(name);
        listener.setResultInit(uploadResult);
        listener.setOrderNosInit(orderNos);
        listener.setOutShipMentMap(outShipmentMap);

        listener.setFileName(name);
        listener.setLocaleMessage(localeMessage);
        listener.setOrderItemTrackingRecords(orderItemTrackingRecords);
        listener.setDataSource(dataSource);
        EasyExcel.read(inputStream, OrderItemTrackingVO.class,listener).sheet().doRead();
        uploadResult = listener.getUploadResult();
        // todo 批量回传订单需要拿到所有的订单号找到对应的shippingNo,从OrderItemTrackingListener拿到导入结果如果成功了拿到orderNos,通过orderNos拿到
        if(uploadResult){
            // 执行多渠道回调tracking
            Set<String> orderExtendIds = listener.getOrderNos();
            if(CollUtil.isNotEmpty(orderExtendIds)){
                List<String> orderExtendIdList = orderExtendIds.stream().collect(Collectors.toList());
                if(CollUtil.isNotEmpty(orderExtendIdList)){
                    List<OrderItemShippingRecord> shippingRecordList =TenantHelper.ignore(()->iOrderItemShippingRecordService.queryListByOrderExtendId(orderExtendIdList)) ;
                    if (CollUtil.isNotEmpty(shippingRecordList)){
                        List<String> shippingNos = shippingRecordList.stream().map(OrderItemShippingRecord::getShippingNo)
                                                                     .collect(Collectors.toList());
                        ThirdWarehouseEvent.passBackTracking(StockManagerEnum.BizArk, shippingNos, outShipmentMap);
                    }
                }

            }
        }
    }

    @Override
    public Orders setOrderBusinessFieldOnlyErp(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        List<SaleOrderItemDTO> orderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();

        String logisticsType = orderReceiveFromThirdDTO.getLogisticsType();



        LogisticsTypeEnum logisticsTypeEnum = LogisticsTypeEnum.getLogisticsTypeEnumByName(logisticsType);
        SaleOrderItemDTO dto = orderItemsList.get(0);
        BigDecimal unitPrice = dto.getUnitPrice();
        String erpSku = dto.getErpSku();
        orders.setLogisticsType(logisticsTypeEnum);
        ProductSkuPrice price ;
        BigDecimal originalProductAmount = BigDecimal.ZERO;

        BigDecimal platformProductAmount = BigDecimal.ZERO;

        BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
        BigDecimal originalPickAmount = BigDecimal.ZERO;

        BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
        BigDecimal platformPickAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount =BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        Long finalSiteId = orders.getSiteId();
        // 可能会有productSkuCode为null的情况,可能是没映射
        if(ObjectUtil.isNotEmpty(erpSku)) {
            price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSku(erpSku, finalSiteId));
            ProductSku sku = TenantHelper.ignore(() -> iProductSkuService.getById(price.getProductSkuId()));
            Product product = TenantHelper.ignore(() -> iProductService.getById(sku.getProductId()));
            for (SaleOrderItemDTO itemDTO : orderItemsList) {
                // 数量*单价--此公式拿的是供应商录入价格
                originalProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                  .multiply(unitPrice);
                // 郭琪:定价公式 单价+操作费+配送费
                // 有会员定价优先使用会员定价
                originalDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                       .multiply(unitPrice
                                                           .add(price.getOriginalOperationFee())
                                                           .add(price.getOriginalFinalDeliveryFee()));
                originalPickAmount = BigDecimal.valueOf(itemDTO.getQuantity()).multiply(unitPrice
                    .add(price.getOriginalOperationFee()));
                platformProductAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                  .multiply(unitPrice);
                platformDropShippingAmount = BigDecimal.valueOf(itemDTO.getQuantity())
                                                       .multiply(unitPrice
                                                           .add(price.getPlatformOperationFee())
                                                           .add(price.getPlatformFinalDeliveryFee()));

                platformPickAmount = BigDecimal.valueOf(itemDTO.getQuantity()).multiply(unitPrice
                    .add(price.getPlatformOperationFee()));

            }
            BigDecimal platformActuralTotalAmount = getPlatformActuralTotalAmount(orderReceiveFromThirdDTO, orders);
            orders.setPlatformTotalPickUpPrice(platformPickAmount);
            orders.setPlatformTotalProductAmount(platformProductAmount);
//            orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);
            orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
            orders.setPlatformRefundExecutableAmount(platformActuralTotalAmount);
            orders.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            // 平台应付总金额
            orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
            // 平台实付总金额
            orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
            // 商品数量*尾程派送费
//            orders.setPlatformTotalFinalDeliveryFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getPlatformFinalDeliveryFee()));
            orders.setPlatformTotalOperationFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getPlatformOperationFee()));
            // 分销商金额 供应商金额
//            orders.setOriginalTotalFinalDeliveryFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getOriginalFinalDeliveryFee()));
            orders.setOriginalTotalOperationFee(BigDecimal.valueOf(dto.getQuantity()).multiply(price.getOriginalOperationFee()));

            orders.setOriginalTotalProductAmount(originalProductAmount);

            orders.setOriginalTotalPickUpPrice(originalPickAmount);
            // 订单类型 drop就拿drop pick就拿pick
            if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
                originalActualTotalAmount = originalDropShippingAmount;
                originalRefundExecutableAmount = originalActualTotalAmount;
            }
            if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                originalActualTotalAmount = originalPickAmount;
                originalRefundExecutableAmount = originalPickAmount;
            }
            orders.setOriginalPayableTotalAmount(originalActualTotalAmount);
            orders.setOriginalActualTotalAmount(originalActualTotalAmount);
            orders.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);
//            orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);
            // 定金暂定0
            orders.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
//        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));

            orders.setTotalQuantity(orderReceiveFromThirdDTO.getTotalQuantity());
            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));


            // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping

            orders.setLogisticsType(logisticsTypeEnum);
            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());


            // 支付状态
            orders.setOrderState(OrderStateType.UnPaid);
            // 物流信息尚未同步 默认给 未发货
            orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orders.setCreateTime(orderReceiveFromThirdDTO.getCreateTime());
            orders.setPayTime(orderReceiveFromThirdDTO.getPaidTime());
            orders.setLatestDeliveryTime(orderReceiveFromThirdDTO.getLatestDeliveryTime());
            orders.setSplit(false);

        }else {
            BigDecimal platformActuralTotalAmount = getPlatformActuralTotalAmount(orderReceiveFromThirdDTO, orders);
            orders.setPlatformTotalPickUpPrice(platformPickAmount);
            orders.setPlatformTotalProductAmount(platformProductAmount);
//            orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);

            // 平台应付总金额
            orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
            // 平台实付总金额
            orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
            orders.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orders.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
//            供应商
            orders.setOriginalTotalProductAmount(BigDecimal.ZERO);

//            orders.setOriginalTotalFinalDeliveryFee(BigDecimal.ZERO);
            orders.setOriginalTotalOperationFee(BigDecimal.ZERO);

            orders.setOriginalTotalPickUpPrice(BigDecimal.ZERO);

//            orders.setOriginalTotalDropShippingPrice(BigDecimal.ZERO);
            orders.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            orders.setOriginalPayableTotalAmount(BigDecimal.ZERO);
            orders.setOriginalActualTotalAmount(BigDecimal.ZERO);
            orders.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
//            orders.setOriginalTotalDropShippingPrice(BigDecimal.ZERO);

            // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
//        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));

            orders.setTotalQuantity(orderReceiveFromThirdDTO.getTotalQuantity());
            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());
//        orders.setPayTime(TimeUtil.localDateTimeToDate(saleOrderDetails.getPaymentDate()));

            // 物流类型 PickUp-自提，DropShipping-代发 ZSMallDropShipping


            orders.setCurrency(orderReceiveFromThirdDTO.getCurrencyCode());

            // 支付状态
            orders.setOrderState(OrderStateType.UnPaid);
//            orders.setPlatformTotalFinalDeliveryFee(BigDecimal.ZERO);
            orders.setFulfillmentProgress(LogisticsProgress.UnDispatched);
//            orders.setPlatformTotalFinalDeliveryFee(BigDecimal.ZERO);
            orders.setPlatformTotalOperationFee(BigDecimal.ZERO);
//            2024.8.12 更新为当前时间
            orders.setCreateTime(orderReceiveFromThirdDTO.getCreateTime());
            orders.setPayTime(orderReceiveFromThirdDTO.getPaidTime());

            orders.setSplit(false);
        }
        // 看看dealPrice那块
        return orders;
    }

    @Override
    public Orders setOrderTagSystemOnlyErp(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        // 看系统内的状态

        // 会影响售后,国外的单不能售后,其余用于搜索查询过滤,默认给了普通订单
        orders.setOrderType(OrderType.Normal);
        // 从渠道店铺拿对应的分销商租户id
        orders.setLineOrderItemId(orderReceiveFromThirdDTO.getLineOrderItemId());
        orders.setTenantId(orderReceiveFromThirdDTO.getTenantId());
        return orders;
    }

    @Override
    public Orders setChannelTagOnlyErp(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        orders.setChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo());
        // 要通过flag和店铺表内的channelAlias 这里得拿systenant
//        SysTenant sysTenant = sysTenantService.getTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(), orders.getChannelType().name());
        if (ObjectUtil.isNull(tenantSalesChannel) || ObjectUtil.isEmpty(tenantSalesChannel.getId())) {
            throw new OrderException("渠道店铺不存在,ChannelFlag为:" + orderReceiveFromThirdDTO.getThirdChannelFlag() + ",请联系管理员手动维护");
        }
        // 设置 channelOrderNoName
        if(null != orderReceiveFromThirdDTO.getIsMultiple() && orderReceiveFromThirdDTO.getIsMultiple()){
            String channelOrderName;
            // 将orderNo后四位截取
            String lastFourChars = null;
            if (StringUtils.isNotEmpty(orders.getOrderNo()) && orders.getOrderNo().length() >= 4) {
                // 使用substring方法截取最后四位
                lastFourChars = orders.getOrderNo().substring(orders.getOrderNo().length() - 4);
            }
            channelOrderName = orderReceiveFromThirdDTO.getOrderNo() + "-" + lastFourChars;
            orders.setChannelOrderName(channelOrderName);
        }else {
            orders.setChannelOrderName(orderReceiveFromThirdDTO.getOrderNo());
        }
        orders.setChannelId(tenantSalesChannel.getId());
        orders.setChannelAlias(orders.getChannelType().name() + ":" + orderReceiveFromThirdDTO.getThirdChannelFlag());
        orders.setChannelOrderName(orderReceiveFromThirdDTO.getOrderNo());
        orders.setChannelOrderTime(orderReceiveFromThirdDTO.getCreateTime());
        orders.setLatestDeliveryTime(orderReceiveFromThirdDTO.getLatestDeliveryTime());
        orders.setPayTime(orderReceiveFromThirdDTO.getPaidTime());
        orders.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        return orders;
    }
    /**
     * 处理订单附件
     * @param startRow 开始行
     * @param finalPageSize 结束行
     * @param dto 查询条件
     * @param temporaryTableName 临时表
     * @throws IOException
     */
    private void dealOrderAttachment(int startRow, int finalPageSize,  OrdersPageBo dto, String temporaryTableName,String rootPath,String exportPath) throws IOException {
        //查询订单/附件记录
        List<OrderAttachmentExportDto> dtos=  ordersMapper.selectOrderAttachment(startRow,finalPageSize,dto);
        //处理临时表
        List<TemporaryTable> temporaryTables = new ArrayList<>();
        //封装临时表
        dtos.forEach(s -> {
            TemporaryTable temporaryTable = new TemporaryTable();
            temporaryTable.setValue3(s.getOrderNo());
            temporaryTables.add(temporaryTable);
        });
        temporaryTableMapper.batchInsertTemporaryTable(temporaryTables,temporaryTableName);
        //获取订单承运商类型
        List<OrderExportListDTO> temporaryTrackingRecord = TenantHelper.ignore(() -> ordersMapper.selectOrderTrackingRecord(temporaryTableName), TenantType.Supplier, TenantType.Manager);
        //处理物流单号
        Map<String, OrderExportListDTO> temporaryTrackingRecordMap = temporaryTrackingRecord.stream().collect(Collectors.toConcurrentMap(
            // key为orderNo
            OrderExportListDTO::getOrderNo,
            // value为OrderExportListDTO对象本身
            Function.identity(),
            (existingValue, newValue) -> {
                // 如果key重复，则拼接logistics，但确保长度不超过200
                if ((existingValue.getLogisticsTrackingNo() + "," + newValue.getLogisticsTrackingNo()).length() <= 210) {
                    existingValue.setLogisticsTrackingNo(existingValue.getLogisticsTrackingNo() + "," + newValue.getLogisticsTrackingNo());
                }
                // 如果拼接后的长度超过200，则不进行拼接，返回现有的existingValue
                return existingValue;
            }
        ));

        //获取文件
        dtos.forEach(s->{
            OrderExportListDTO orderExportListDTO = temporaryTrackingRecordMap.get(s.getOrderNo());
            if (ObjectUtil.isNotNull(orderExportListDTO)){
                s.setLogisticsCarrier(orderExportListDTO.getLogisticsCarrier());
                s.setLogisticsTrackingNo(orderExportListDTO.getLogisticsTrackingNo());
            }
            //文件后缀名
            String extension ="."+ s.getOssUrl().split("\\.")[s.getOssUrl().split("\\.").length - 1];
            String downloadPath=rootPath+"/"+s.getLogisticsCarrier();
            String copyPath=exportPath+"/"+s.getLogisticsCarrier();
            String filePoolName=s.getOrderNo()+"_"+s.getOrderAttachmentId();
            String fileNewName=s.getOrderNo()+"_"+s.getLogisticsTrackingNo();
            //redis中获取文件
            Object cacheMapValue = RedisUtils.getCacheMapValue(GlobalConstants.ORDER_ATTACHMENT,  filePoolName);
            if (ObjectUtil.isNotNull(cacheMapValue)){
                //如果存在，从公共目录里面拷贝到临时目录
                // 检查目标文件是否存在
                Path targetPath = Paths.get(copyPath + "/" + fileNewName + extension);
                // 检查目标文件是否存在，如果存在则重命名
                int counter = 1;
                while (Files.exists(targetPath)) {
                    // 在文件名中加入计数器来生成新的文件名
                    String newFileName = fileNewName + "_" + counter + extension;
                    targetPath = Path.of(copyPath, newFileName);
                    counter++;
                }
                FileUtils.copy(Paths.get(downloadPath +"/"+ filePoolName + extension),targetPath, StandardCopyOption.REPLACE_EXISTING);
            }else {
                //判断当前文件是否在文件池中已经存在
                boolean empty = FileUtils.isEmpty(Paths.get(downloadPath +"/"+ filePoolName + extension).toFile());
                if (empty){
                    try {
                        downloadFile(s.getOssUrl(),filePoolName,fileNewName,downloadPath,copyPath,extension);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }else {
                    //上传到redis
                    HashMap<String, String> map = new HashMap<>();
                    map.put(filePoolName,s.getOssUrl());
                    RedisUtils.setCacheMap(GlobalConstants.ORDER_ATTACHMENT,map);
                    //将公共地方的文件拷贝一份到下载目录
                    Path targetPath = Paths.get(copyPath + "/" + fileNewName + extension);
                    // 检查目标文件是否存在，如果存在则重命名
                    int counter = 1;
                    while (Files.exists(targetPath)) {
                        // 在文件名中加入计数器来生成新的文件名
                        String newFileNames = fileNewName + "_" + counter + extension;
                        targetPath = Path.of(copyPath, newFileNames);
                        counter++;
                    }
                    FileUtils.copy(Paths.get(downloadPath +"/"+ filePoolName + extension),targetPath,StandardCopyOption.REPLACE_EXISTING);
                }
            }
        });
        log.info(StrUtil.format("第{}批次下载完成",startRow));
    }

    /**
     * 下载OSS文件
     * @param ossUrl       oss文件地址
     * @param filePoolName 公共文件夹名称
     * @param fileNewName 新的文件名称
     * @param downloadPath 下载的文件夹地址
     * @param copyPath 拷贝的文件夹地址
     * @param extension 文件后缀名
     * @throws IOException
     */
    public void downloadFile(String ossUrl,String filePoolName,String fileNewName,String downloadPath,String copyPath,String extension) throws IOException {
        if (ObjectUtil.isEmpty(ossUrl)){
            return;

        }
        // 指定要保存文件的本地目录
        // 新文件名
        String newFileName = filePoolName+extension;
        // 创建目录（如果不存在）
        Files.createDirectories(Paths.get(downloadPath));
        // 完整的本地文件路径
        String localFilePath = downloadPath +"/"+ newFileName;
        try {
            // 创建URL对象
            URL url = new URL(ossUrl);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置连接和读取超时时间（例如：30秒）
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);

            // 获取输入流
            InputStream in = new BufferedInputStream(connection.getInputStream());
            // 创建文件输出流
            FileOutputStream fileOutputStream = new FileOutputStream(localFilePath);

            byte[] dataBuffer = new byte[1024];
            int bytesRead;
            // 读取并写入文件
            while ((bytesRead = in.read(dataBuffer, 0, 1024)) != -1) {
                fileOutputStream.write(dataBuffer, 0, bytesRead);
            }
            // 关闭流
            fileOutputStream.close();
            in.close();
            log.info("文件下载并重命名成功: " + localFilePath);
            //上传到redis
            HashMap<String, String> map = new HashMap<>();
            map.put(filePoolName,ossUrl);
            RedisUtils.setCacheMap(GlobalConstants.ORDER_ATTACHMENT,map);
            //将公共地方的文件拷贝一份到下载目录

            //如果存在，从公共目录里面拷贝到临时目录
            // 检查目标文件是否存在
            Path targetPath = Paths.get(copyPath + "/" + fileNewName + extension);
            // 检查目标文件是否存在，如果存在则重命名
            int counter = 1;
            while (Files.exists(targetPath)) {
                // 在文件名中加入计数器来生成新的文件名
                String newFileNames = fileNewName + "_" + counter + extension;
                targetPath = Path.of(copyPath, newFileNames);
                counter++;
            }

            FileUtils.copy(Paths.get(downloadPath +"/"+ filePoolName + extension),targetPath,StandardCopyOption.REPLACE_EXISTING);
        } catch (java.net.SocketTimeoutException e) {
            System.out.println("下载超时，请检查网络连接或尝试增加超时时间");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("文件下载失败");
        }
    }

    /**
     * 分销商申请订单取消
     * @param orderNo
     */
    @Override
    public void cancelOrder(String orderNo) {
        //取消订单
        Orders orders = iOrdersService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(orders)) {
            throw new RuntimeException("订单不存在" + orderNo);
        }
        if (ObjectUtil.isNull(orders.getOrderExtendId())) {
            throw new RuntimeException("订单行ID不存在" + orderNo);
        }
        if (orders.getCancelStatus() != 0 && (
            ObjectUtil.notEqual(orders.getFulfillmentProgress().name(), LogisticsProgress.UnDispatched.name())
                &&
                ObjectUtil.notEqual(orders.getFulfillmentProgress().name(), LogisticsProgress.Abnormal.name()))) {
            throw new RuntimeException("订单状态不合法,只支持待发货且未取消过的订单");
        }
        LambdaQueryWrapper<com.zsmall.order.entity.domain.OrderItem> q = new LambdaQueryWrapper<>();
        q.eq(com.zsmall.order.entity.domain.OrderItem::getOrderId, orders.getId());
        com.zsmall.order.entity.domain.OrderItem orderItem = TenantHelper.ignore(() -> iOrderItemService.getBaseMapper()
                                                                                                        .selectOne(q));
        if (ObjectUtil.isNull(orderItem)) {
            throw new RuntimeException("订单明细不存在" + orders.getId());
        }
        iOrderManger.cancelOrderFlow(ListUtil.of(orders.getOrderExtendId()), orderItem.getTenantId(), orderItem.getSupplierTenantId());
    }


    @Override
    public void updateOrderCancelStatus(String orderNo, Integer cancelStatus) throws Exception {
        //cancel_status 0:无，1取消中，2取消成功，3取消失败
        List<Integer> integers = List.of(2, 3);
        if (!integers.contains(cancelStatus)) {
            throw new RuntimeException("非法的请求参数");
        }
        //取消订单
        Orders orders = iOrdersService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(orders)) {
            throw new RuntimeException("订单不存在" + orderNo);
        }
        if (ObjectUtil.isNull(orders.getOrderExtendId())) {
            throw new RuntimeException("订单行ID不存在" + orderNo);
        }
        LogisticsProgress fulfillmentProgress = orders.getFulfillmentProgress();
        if (orders.getCancelStatus() != 1 || ObjectUtil.notEqual(fulfillmentProgress
            .name(), LogisticsProgress.UnDispatched.name())) {
            throw new RuntimeException("订单状态不合法,只支持待发货且取消中的订单更新取消状态");
        }
        List<OrderRefund> orderRefunds = getOrderRefundsByOrderExtendId(orders.getOrderExtendId());
        if (CollUtil.isEmpty(orderRefunds)) {
            throw new RuntimeException("[系统异常]，未查到退款单信息");
        }
        List<String> orderRefundNo = orderRefunds.stream().map(OrderRefund::getOrderRefundNo)
                                                 .collect(Collectors.toList());
        Boolean isAbnormal = LogisticsProgress.Abnormal.equals(fulfillmentProgress);
        //取消成功
        if (cancelStatus == 2) {
            agreeSuccess(orderRefundNo, orders.getOrderExtendId(), false,isAbnormal);
        }
        //取消失败,审批成失败
        if (cancelStatus == 3) {
            if (CollUtil.isNotEmpty(orderRefundNo)) {
                refuseCancel(orderRefundNo, orders.getOrderExtendId(), false);
            }
        }

    }
    @Override
    public List<OrderRefund> getOrderRefundsByOrderExtendId(String orderExtendId) {
        List<Orders> orders = iOrdersService.getByOrderExtendId(orderExtendId);
        if (CollUtil.isEmpty(orders)) {
            throw new RuntimeException("未匹配到订单");
        }
        List<String> orderNos = orders.stream()
                                       .map(Orders::getOrderNo)
                                       .collect(Collectors.toList());
        //查询退款单状态
        LambdaQueryWrapper<OrderRefund> q = new LambdaQueryWrapper<>();
        q.in(OrderRefund::getOrderNo, orderNos);
        q.eq(OrderRefund::getDelFlag, 0);
        List<OrderRefund> orderRefunds = TenantHelper.ignore(() -> iOrderRefundService.getBaseMapper().selectList(q));
        return orderRefunds;
    }

    /**
     * 功能描述：拒绝取消
     *
     * @param orderRefundNo 订单退款编号
     * @param supplierCall
     * <AUTHOR>
     * @date 2025/06/12
     */
    @Override
    public void refuseCancel(List<String> orderRefundNo, String orderExtendId, Boolean supplierCall) throws Exception {
        for (String orderRefund : orderRefundNo) {
            String tenantType = LoginHelper.getTenantType();
            if(supplierCall){
                tenantType = TenantType.Supplier.name();
            }
            RefundApplyHandleBo bo = new RefundApplyHandleBo();
//            bo.setOrderRefundNoList();
            bo.setOrderRefundNo(orderRefund);
//                    bo.setOrderRefundItemNo();
            bo.setPass(Boolean.FALSE);
            bo.setIsAuto(Boolean.FALSE);
            bo.setRefundApplyType("Refund");

            if (ObjectUtil.isNotEmpty(tenantType) && ObjectUtil.equals(tenantType, TenantType.Supplier.name())) {
                orderRefundServiceImpl.refundApplyHandleSupplier(bo);
            } else {// 管理員处理
                orderRefundServiceImpl.refundApplyHandleManager(bo);
            }
        }
        TenantHelper.ignore(() -> ordersService.cancelFailed(orderExtendId));
    }
    @Override
    public void agreeSuccess(List<String> orderRefundNo, String orderExtendId, Boolean supplierCall,Boolean isAbnormal) {
        SubmitRefundApplyBo submitRefundApplyBo = new SubmitRefundApplyBo();
        submitRefundApplyBo.setOrderRefundNos(orderRefundNo);
        submitRefundApplyBo.setIsAbnormal(isAbnormal);
        //todo 手动管理员取消流程,保持原逻辑
        iOrderManger.refundSuccessProcess(orderExtendId, submitRefundApplyBo, false, supplierCall);
    }

    @Override
    public void canceling2CancelFlow() {

        // 查出所有取消中的订单,调用erp查询接口,判断是否已经发货
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Orders::getOrderState,OrderStateType.Paid);
        queryWrapper.eq(Orders::getFulfillmentProgress,LogisticsProgress.UnDispatched);
        queryWrapper.eq(Orders::getCancelStatus, 1);
        queryWrapper.eq(Orders::getDelFlag, 0);
        List<Orders> orders = TenantHelper.ignore(()->ordersMapper.selectList(queryWrapper));
        // orders筛选出集合内修改时间到当前时间间隔大于5分钟的订单;
        List<String> orderNos = orders.stream().map(o -> o.getOrderNo()).collect(Collectors.toList());
        List<com.zsmall.order.entity.domain.OrderItem> itemList = TenantHelper.ignore(() -> iOrderItemService.getList(orderNos));
        Date now = new Date();
        //
        List<String> autoTenantIds = iSysApiService.getSysConfigByTenantIdAllowPush();
        if (CollUtil.isEmpty(autoTenantIds)){
            log.info("没有支持取消的供应商订单");
            return;
        }
        List<com.zsmall.order.entity.domain.OrderItem> needCancelOrderItems = itemList.stream().filter(order -> { // 条件1：更新时间超过5分钟
            Date updateTime = order.getUpdateTime();
            if (updateTime == null) {
                return false;
            }
            long diffMillis = now.getTime() - updateTime.getTime();
            boolean isOver5Minutes = (diffMillis / (1000 * 60)) > 5; // 条件2：租户ID在白名单内
            boolean isTenantAllowed = autoTenantIds.contains(order.getSupplierTenantId()); // 同时满足两个条件
            return isOver5Minutes && isTenantAllowed;
        }).collect(Collectors.toList());

        if(CollUtil.isEmpty(needCancelOrderItems)){
            log.info("没有需要取消的订单");
            return;
        }

        Map<String, String> orderNoAndTenantIdMap = needCancelOrderItems.stream()
                                                              .collect(Collectors.toMap(com.zsmall.order.entity.domain.OrderItem::getOrderNo, com.zsmall.order.entity.domain.OrderItem::getSupplierTenantId));
        Set<String> needCancelOrderNos = needCancelOrderItems.stream()
                                                             .map(com.zsmall.order.entity.domain.OrderItem::getOrderNo)
                                                             .collect(Collectors.toSet());
        List<Orders> filteredOrders = orders.stream()
                                           .filter(order -> needCancelOrderNos.contains(order.getOrderNo()))
                                           .collect(Collectors.toList());

        ArrayList<String> orderNoList = new ArrayList<>();
        for (Orders needCancelOrder : filteredOrders) {
            String orderNo = needCancelOrder.getOrderNo();
            String tenantId = needCancelOrder.getTenantId();
            // LTL 需要合单
            if(orderNoList.contains(needCancelOrder.getOrderExtendId())){
                continue;
            }else {
                orderNoList.add(needCancelOrder.getOrderExtendId());
            }
            // 先查询erp有没有取消结果,有就取消成功或失败
            String supplierTenantId  = orderNoAndTenantIdMap.get(needCancelOrder.getOrderNo());
            ThebizarkDelegate delegate = initDelegate(supplierTenantId);
            Result<List<CancelOutOrder>> listResult = delegate.orderApi()
                                                              .queryCancelOrderV2(needCancelOrder.getOrderExtendId());
            if(ObjectUtil.isNotEmpty(listResult)){
                List<CancelOutOrder> data = listResult.getData();
                if (CollUtil.isNotEmpty(data)){
                    CancelOutOrder outOrder = data.get(0);
                    //            0拦截中 1拦截成功 2拦截失败 -1接口调用失败或异常 3部分拦截
                    Integer deliverInterceptStatus = outOrder.getDeliverInterceptStatus();
                    // 拦截成功
                    if(ObjectUtil.isNotEmpty(deliverInterceptStatus)){
                        // 走提交退款流程
                        TenantHelper.ignore(()->iOrderManger.processRefundsAuto(deliverInterceptStatus,orderNo,tenantId, false));
                    }
                }else {
                    TenantHelper.ignore(()->iOrderManger.processRefundsAuto(2,orderNo,tenantId, false));
                }
            }else {
                TenantHelper.ignore(()->iOrderManger.processRefundsAuto(2,orderNo,tenantId, false));
            }

        }
    }


    /**
     * OpenAPI供应商订单发货
     * @param shippingRecordList
     * @param ordersDeliveryDTO
     */
    @Override
    public void openApiOrderDelivery(List<OrderItemShippingRecord> shippingRecordList, OpenApiOrdersDeliveryDTO ordersDeliveryDTO) {
        ShippingStateEnum shippingState = ShippingStateEnum.valueOfByBizArk(ordersDeliveryDTO.getShippingState());
        // 渠道订单号集合
        List<String> channelOrderNoList = new ArrayList<>();
        // 多渠道tracking信息集合
        List<TrackingCallBackVo> trackingCallBackVoList = new ArrayList<>();
        // erp订单状态集合
        Map<String, ShippingStateEnum> orderStatusMap = new HashMap<>();
        if (CollUtil.isNotEmpty(shippingRecordList)) {
            for (OrderItemShippingRecord shippingRecord : shippingRecordList) {
                String orderNo = shippingRecord.getOrderNo();
                String orderItemNo = shippingRecord.getOrderItemNo();
                String shippingNo = shippingRecord.getShippingNo();

                com.zsmall.order.entity.domain.OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                Orders orders = iOrdersService.getByOrderNo(orderNo);

                // 取消中/取消成功不支持发货
                if (orders.getCancelStatus()!=0 && orders.getCancelStatus()!=3) {
                    shippingRecord.setSystemManaged(false);
                    iOrderItemShippingRecordService.updateById(shippingRecord);
                    throw new RuntimeException("订单状态为取消中/取消成功/取消异常不支持发货！"+shippingRecord.getOrderNo());
                }

                LogisticsProgress fulfillmentProgress = orders.getFulfillmentProgress();
                if (ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched)){
                    continue;
                }
                // 查询关联的子订单是否已经发货，如果发货，则此ShippingOrders不再需要定时器轮询
                if (ObjectUtil.notEqual(fulfillmentProgress, LogisticsProgress.UnDispatched)){
                    shippingRecord.setSystemManaged(false);
                    iOrderItemShippingRecordService.updateById(shippingRecord);
                    throw new RuntimeException("订单状态不是待发货状态，不支持发货！"+shippingRecord.getOrderNo());
                }

                ShippingStateEnum oldShippingState = shippingRecord.getShippingState();
                String supplierTenantId = shippingRecord.getSupplierTenantId();
                if (shippingRecord.getSystemManaged() == null) {
                    shippingRecord.setSystemManaged(true);
                }
                Orders order = iOrdersService.getByOrderNo(orderNo);
                // 查询当前订单是否是LTL订单
                OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
                try {
                    //  OutOrder outOrder = result.getData();
                    String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();
                    LambdaQueryWrapper<Warehouse> q = new LambdaQueryWrapper<>();
                    q.eq(Warehouse::getWarehouseType,WarehouseTypeEnum.BizArk.name());
                    q.eq(Warehouse::getTenantId,supplierTenantId);
                    q.eq(Warehouse::getWarehouseSystemCode,warehouseSystemCode);
                    Warehouse warehouse = iWarehouseService.getBaseMapper().selectOne(q);
                    if (warehouse != null) {
                        warehouseSystemCode = warehouse.getWarehouseSystemCode();
                    }
                    // 记录订单状态
                    if (!orderStatusMap.containsKey(order.getOrderNo())) {
                        orderStatusMap.put(order.getOrderNo(), shippingState);
                    }
                    // 已发货的才继续往下走
                    // 2022-8-2调整，已拣货的也继续往下走，但是只同步物流跟踪信息，不修改状态为已发货
                    // 当前查询的恒健仓库物流单状态为已拣货或已发货，且与之前的ShippingOrders的老状态不相等时，才进入if处理跟踪单数据
                    if ((ObjectUtil.equals(shippingState, ShippingStateEnum.Picked)
                        || ObjectUtil.equals(shippingState, ShippingStateEnum.Shipped))
                        && ObjectUtil.notEqual(shippingState, oldShippingState)) {
                        // log.info("queryBizarkTacking shippingOrderId = {} shipmentPackages = {}",
                        // shippingOrderId, JSONUtil.toJsonStr(shipmentPackages));
                        // XxlJobHelper.log("queryBizarkTacking shippingOrderId = {} shipmentPackages = {}",
                        // shippingOrderId, JSONUtil.toJsonStr(shipmentPackages));

                        // 旧的跟踪单信息，需要全删全增
                        // List<OrderItemTrackingRecord> oldTrackingOrders =
                        // iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                        // log.info("queryBizarkTacking - trackingOrders.size = {}",
                        // CollectionUtils.size(oldTrackingOrders));
                        // 多渠道回调信息
                        List<OpenApiOrdersDeliveryDTO.OpenApiOrderOutShipment> shipmentPackages = ordersDeliveryDTO.getOrderShipments();
                        TrackingCallBackVo trackingCallBackVo = new TrackingCallBackVo();
                        // 需要新创建的物流单
                        List<OrderItemTrackingRecord> newTrackingRecords = new ArrayList<>();
                        // LTL的PO订单,创建发货单方式是一单多品,Tracking回传 多品采用相同的tracking
                        if (ObjectUtil.isNotNull(orderLogisticsInfo) && CarrierTypeEnum.LTL.name()
                                                                                           .equals(orderLogisticsInfo.getLogisticsCarrierCode())) {
                            shipmentPackages = List.of(shipmentPackages.get(0));
                        }
                        for (OpenApiOrdersDeliveryDTO.OpenApiOrderOutShipment shipmentPackage : shipmentPackages) {
                            // 将恒健仓库物流信息转成物流信息
                            OrderItemTrackingRecord trackingRecord =
                                this.convertTrackingRecord(shipmentPackage, shippingRecord, orderItem, orderItemProductSku, warehouseSystemCode, shippingState);

                            // 添加到待保存的列表中
                            newTrackingRecords.add(trackingRecord);
                        }
                        if (org.apache.commons.lang3.StringUtils.isNotEmpty(order.getChannelOrderNo())) {
                            if (null != order.getChannelId()) {
                                if (null == order.getTrackingFlag() || order.getTrackingFlag().equals(1)) {
                                    TenantSalesChannel tenantSalesChannel =
                                        iTenantSalesChannelService.selectByIdNotTenant(order.getChannelId());
                                    List<String> channelNames =
                                        Arrays.asList(
                                            "FineJoys Home",
                                            "MyDepot Outlet",
                                            "Mydepot3-US",
                                            "Tik Tok-MyDepot4店-US");
                                    boolean channelNameFlag =
                                        channelNames.contains(tenantSalesChannel.getChannelName());

                                    boolean channelFlag =
                                        org.apache.commons.lang3.StringUtils.isNotEmpty(tenantSalesChannel.getConnectStr())
                                            && null != tenantSalesChannel.getState()
                                            && tenantSalesChannel.getState().equals(1)
                                            || channelNameFlag;
                                    //                                                if(null !=
                                    // tenantSalesChannel &&
                                    // StringUtils.isNotEmpty(tenantSalesChannel.getConnectStr()) && null !=
                                    // tenantSalesChannel.getState() && tenantSalesChannel.getState().equals(1)){
                                    if (null != tenantSalesChannel && channelFlag) {
                                        if (org.apache.commons.lang3.StringUtils.isNotEmpty(order.getChannelOrderNo())
                                            && !channelOrderNoList.contains(order.getChannelOrderNo())) {
                                            if ( ObjectUtil.equals(shippingState,ShippingStateEnum.Shipped)){
                                                trackingCallBackVo
                                                    .setOrderNum(order.getChannelOrderNo())
                                                    .setChannelId(tenantSalesChannel.getChannelName())
                                                    .setShipWarehouse(warehouseSystemCode);
                                                trackingCallBackVoList.add(trackingCallBackVo);
                                                log.info("回传tracking订单：{}", trackingCallBackVo);
                                                channelOrderNoList.add(order.getChannelOrderNo());
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        shippingRecord.setShippingState(shippingState);
                        // 新的跟踪单集合不为空，保存新的跟踪单，并把旧的跟踪单都解除关联
                        if (CollectionUtils.isNotEmpty(newTrackingRecords)) {
                            iOrderItemTrackingRecordService.trackingListDisassociate(orderItemNo);
                            iOrderItemTrackingRecordService.saveBatch(newTrackingRecords);
                        }

                        // // 子订单不为空，开始回传物流信息至渠道
                        // log.info("queryBizarkTacking - newTrackingRecords.size = {} saveOrderItems.size =
                        // {} shippingStatusType = {}",
                        //     CollectionUtils.size(newTrackingRecords),
                        // CollectionUtils.size(saveOrderItems), shippingStatusType);
                        if (Objects.equals(shippingState, ShippingStateEnum.Shipped)) {
                            // 恒健仓库回传状态为已发货或已拣货，并且存在物流商和单号时，该出货单不再需要定时器轮询
                            shippingRecord.setSystemManaged(false);
                            shippingRecord.setShippingErrorCode(null);
                            shippingRecord.setShippingErrorMessage(null);

                            // 同步物流信息至第三方渠道
                            ThirdChannelFulfillmentRecord thirdChannelFulfillmentRecord =
                                new ThirdChannelFulfillmentRecord();
                            thirdChannelFulfillmentRecord.setChannelId(orderItem.getChannelId());
                            thirdChannelFulfillmentRecord.setChannelType(order.getChannelType());
                            thirdChannelFulfillmentRecord.setOrderNo(order.getOrderExtendId());
                            thirdChannelFulfillmentRecord.setOrderItemNo(orderItem.getOrderItemNo());
                            thirdChannelFulfillmentRecord.setChannelItemNo(orderItem.getChannelItemNo());
                            thirdChannelFulfillmentRecord.setChannelOrderNo(order.getChannelOrderNo());
                            thirdChannelFulfillmentRecord.setChannelOrderName(order.getChannelOrderName());
                            thirdChannelFulfillmentRecord.setFulfillmentPushState(
                                FulfillmentPushStateEnum.WaitPush);
                            iThirdChannelFulfillmentRecordService.save(thirdChannelFulfillmentRecord);
                        }

                    }
                } catch (Exception e) {
                    shippingRecord.setShippingErrorCode(e.getMessage());
                    log.error("恒健仓库【查询发货单】 发货单号{}，发生未知错误 {}", shippingNo, e.getMessage(), e);
                }
                //更新子订单的发货状态
                if (ObjectUtil.equals(shippingState, ShippingStateEnum.Shipped)){
                    order.setFulfillmentProgress(LogisticsProgress.Dispatched);
                    orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
                }
                if (ObjectUtil.equals(shippingState, ShippingStateEnum.Picked)){
                    order.setFulfillmentProgress(LogisticsProgress.UnDispatched);
                    orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
                }
                iOrdersService.updateById(order);
                iOrderItemShippingRecordService.updateById(shippingRecord);
                iOrderItemService.updateById(orderItem);
                iOrderItemProductSkuService.updateById(orderItemProductSku);
//                  SetOrderFulfillmentProgressEvent event =
//                      new SetOrderFulfillmentProgressEvent(order.getId());
//                  SpringUtils.publishEvent(event);
            }
            log.info("订单tracking信息回传头信息: {}", JSONUtil.toJsonStr(trackingCallBackVoList));
            if (CollectionUtil.isNotEmpty(trackingCallBackVoList)) {
                List<String> orderNums =
                    trackingCallBackVoList.stream()
                                          .map(TrackingCallBackVo::getOrderNum)
                                          .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(orderNums)) {
                    List<Orders> channelOrderNoIn = iOrdersService.findByChannelOrderNoIn(orderNums);
                    List<OrderItemTrackingRecord> listByOrderNo = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(channelOrderNoIn)) {
                        List<String> orderNos =
                            channelOrderNoIn.stream().map(Orders::getOrderNo).collect(Collectors.toList());
                        listByOrderNo = iOrderItemTrackingRecordService.getListByOrderNo(orderNos);
                    }
                    // 没有tracking信息的订单号
                    List<TrackingCallBackVo> trackingCallBackVoListNotCallBack = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(channelOrderNoIn)
                        && CollectionUtil.isNotEmpty(listByOrderNo)) {
                        loop2:
                        for (TrackingCallBackVo trackingCallBackVo : trackingCallBackVoList) {
                            String shipDate = null;
                            List<TrackingCallBackPackagesVo> packages = new ArrayList<>();
                            loop:
                            for (Orders orders : channelOrderNoIn) {
                                if (trackingCallBackVo.getOrderNum().equals(orders.getChannelOrderNo())) {
                                    for (OrderItemTrackingRecord orderItemTrackingRecord : listByOrderNo) {
                                        if (orders.getOrderNo().equals(orderItemTrackingRecord.getOrderNo())) {
                                            TrackingCallBackPackagesVo trackingCallBackPackagesVo =
                                                new TrackingCallBackPackagesVo();
                                            // 判断当前的订单的发货状态，如果不为shipped状态，则不能回传
                                            if (CollUtil.isNotEmpty(orderStatusMap)) {
                                                ShippingStateEnum orderStatue =
                                                    orderStatusMap.get(orders.getOrderExtendId());
                                                if (null != orderStatue
                                                    && !ObjectUtil.equals(orderStatue, ShippingStateEnum.Shipped)) {
                                                    log.warn("订单状态为:{}，订单信息:{},不能回传", orderStatue, orders);
                                                    trackingCallBackVoListNotCallBack.add(trackingCallBackVo);
                                                    continue loop2;
                                                }
                                            }
                                            // trackingNo为空或者渠道商为空的排除
                                            if (org.apache.commons.lang3.StringUtils.isEmpty(orderItemTrackingRecord.getLogisticsCarrier())
                                                || org.apache.commons.lang3.StringUtils.isEmpty(
                                                orderItemTrackingRecord.getLogisticsTrackingNo())) {
                                                log.warn("订单tracking信息为空，订单tracking信息:{},", orderItemTrackingRecord);
                                                trackingCallBackVoListNotCallBack.add(trackingCallBackVo);
                                                continue loop2;
                                            }
                                            trackingCallBackPackagesVo
                                                .setCarrier(orderItemTrackingRecord.getLogisticsCarrier())
                                                .setTrackingNo(orderItemTrackingRecord.getLogisticsTrackingNo());
                                            if (null != orderItemTrackingRecord.getDispatchedTime()) {
                                                String shipDate1 =
                                                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                                        .format(orderItemTrackingRecord.getDispatchedTime());
                                                trackingCallBackPackagesVo.setShipDate(shipDate1);
                                                if (null == shipDate) {
                                                    shipDate = shipDate1;
                                                }
                                            }
                                            TrackingCallBackPackagesItemsVo trackingCallBackPackagesItemsVo =
                                                new TrackingCallBackPackagesItemsVo();
                                            trackingCallBackPackagesItemsVo
                                                .setLineNum(orders.getLineOrderItemId())
                                                .setShipedQuantity(orderItemTrackingRecord.getQuantity())
                                                .setSellerSku(orderItemTrackingRecord.getSku());
                                            trackingCallBackPackagesVo.setItems(
                                                Arrays.asList(trackingCallBackPackagesItemsVo));
                                            packages.add(trackingCallBackPackagesVo);
                                            continue loop;
                                        }
                                    }
                                }
                            }
                            trackingCallBackVo.setPackages(packages);
                            trackingCallBackVo.setShipDate(shipDate);
                        }
                        // 去除没有tracking信息的订单
                        log.info(
                            "没有tracking信息的订单: {}, size: {}",
                            JSONUtil.toJsonStr(trackingCallBackVoListNotCallBack),
                            trackingCallBackVoListNotCallBack.size());
                        if (CollUtil.isNotEmpty(trackingCallBackVoListNotCallBack)) {
                            trackingCallBackVoList.removeAll(trackingCallBackVoListNotCallBack);
                        }
                        try {
                            log.info(
                                "订单回传tracking信息至多渠道的全部tracking信息: {}",
                                JSONUtil.toJsonStr(trackingCallBackVoList));
                            Integer batchSize = 1000;
                            Iterator<TrackingCallBackVo> iterator = trackingCallBackVoList.iterator();
                            List<TrackingCallBackVo> batch = new ArrayList<>(batchSize);
                            while (iterator.hasNext()) {
                                TrackingCallBackVo trackingCallBackVo = iterator.next();
                                batch.add(trackingCallBackVo);
                                if (batch.size() == batchSize) {
                                    log.info("订单回传tracking信息至多渠道,tracking信息: {}", JSONUtil.toJsonStr(batch));
                                    String str = JSON.toJSONString(batch);
                                    String messageId = IdUtil.fastSimpleUUID();
                                    Message message =
                                        MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                    rabbitTemplate.convertAndSend(
                                        RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,
                                        RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY,
                                        message);
                                    List<String> orderNumList =
                                        batch.stream()
                                             .map(TrackingCallBackVo::getOrderNum)
                                             .collect(Collectors.toList());
                                    if (CollUtil.isNotEmpty(orderNumList)) {
                                        iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList, 2);
                                    }
                                    // 清空列表
                                    batch.clear();
                                }
                            }
                            // 剩余的
                            if (!batch.isEmpty()) {
                                log.info("订单回传tracking信息至多渠道,tracking信息: {}", JSONUtil.toJsonStr(batch));
                                String str = JSON.toJSONString(batch);
                                String messageId = IdUtil.fastSimpleUUID();
                                Message message =
                                    MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
                                rabbitTemplate.convertAndSend(
                                    RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,
                                    RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY,
                                    message);
                                List<String> orderNumList =
                                    batch.stream()
                                         .map(TrackingCallBackVo::getOrderNum)
                                         .collect(Collectors.toList());
                                if (CollUtil.isNotEmpty(orderNumList)) {
                                    iOrdersService.updateOrderTrackingFlagByChannelOrderNo(orderNumList, 2);
                                }
                                // 清空批次列表
                                batch.clear();
                            }
                        } catch (Exception e) {
                            log.info(
                                "订单回传tracking信息至多渠道,tracking信息: {}, 出现异常: {}",
                                JSONUtil.toJsonStr(trackingCallBackVoList),
                                e.getMessage());
                        }
                    }
                }
            }
        }
    }


    /**
     * 将恒健仓库物流信息转成TrackingRecord
     */
    private OrderItemTrackingRecord convertTrackingRecord(OpenApiOrdersDeliveryDTO.OpenApiOrderOutShipment shipmentPackage,
                                                          OrderItemShippingRecord shippingOrder,
                                                          com.zsmall.order.entity.domain.OrderItem orderItem, OrderItemProductSku orderItemProductSku,
                                                          String warehouseSystemCode, ShippingStateEnum shippingState) {
        String carrier = shipmentPackage.getCarrier();
        String trackingNo = shipmentPackage.getTrackingNo();
        Integer shipedQuantity = shipmentPackage.getShippedQuantity();
        DateTime shipmentDate = shipmentPackage.getShipmentDate();

        // 承运商判断，转大写后存在FED的为FedEx，存在UPS的为UPS
        String newCarrier = carrier;
        if (StrUtil.contains(carrier.toUpperCase(), "FED")) {
            newCarrier = "FedEx";
        } else if (StrUtil.contains(carrier.toUpperCase(), "UPS")) {
            newCarrier = "UPS";
            //Added by Buddy @2024-10-19 增加OnTrac物流方式（erp新增OnTrac）分销进行适配
        } else if (StrUtil.contains(carrier.toUpperCase(), "ONTRAC")) {
            newCarrier = "OnTrac";
        }

        log.info("queryBizarkTacking - carrier = {}, newCarrier = {}", carrier, newCarrier);
        OrderItemTrackingRecord newTracking = new OrderItemTrackingRecord();
        newTracking.setShippingNo(shippingOrder.getShippingNo());
        newTracking.setLogisticsCarrier(newCarrier);
        newTracking.setLogisticsTrackingNo(trackingNo);
        newTracking.setQuantity(shipedQuantity);
        newTracking.setOrderNo(shippingOrder.getOrderNo());
        newTracking.setOrderItemNo(shippingOrder.getOrderItemNo());
        newTracking.setSku(orderItemProductSku.getSku());
        newTracking.setWarehouseCode(warehouseSystemCode);
        newTracking.setWarehouseSystemCode(warehouseSystemCode);
        newTracking.setLogisticsService(shipmentPackage.getCarrier());
        orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);

        // 已发货，进行相关操作
        if (ObjectUtil.equals(shippingState, ShippingStateEnum.Shipped)) {
            // 已发货且发货时间不为空，直接取用恒健仓库的发货时间
            if (shipmentDate != null) {
                //将UTC时间转为北京时间
                shipmentDate =DateUtil.offsetHour(shipmentDate,8);
                newTracking.setDispatchedTime(shipmentDate);
                orderItem.setDispatchedTime(shipmentDate);
            } else {  // 已发货但发货时间为空，取用当前系统时间
                newTracking.setDispatchedTime(DateTime.now());
                orderItem.setDispatchedTime(DateTime.now());
            }
            // 子订单改为已发货
            orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
            // 已发货，查询一次物流信息
//            ZSMallOrderEventUtils.queryLogistics(newTracking);
        }
        return newTracking;
    }



    @Override
    public R<Void> exportNewByEs(OrdersPageBo ordersPageBo, HttpServletResponse response) {
        String tenantType = LoginHelper.getTenantType();
        if (StrUtil.isBlank(tenantType)) {
            return R.fail("请先登录");
        }

        List<Warehouse> warehouses =TenantHelper.ignore(()->warehouseService.getBaseMapper().selectList()) ;
        Map<String, Warehouse> warehouseMap = warehouses.stream()
                                                        .collect(Collectors.toMap(Warehouse::getWarehouseSystemCode, warehouse -> warehouse));

        LambdaEsQueryWrapper<EsOrdersDTO> wa = getEsOrdersQueryWrapper(ordersPageBo);
        wa.orderByDesc(EsOrdersDTO::getId);
        String fileName = StrUtil.format(FileNameConstants.ORDER_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.OrdersExport, tempFileSavePath -> {
            try {
                TimeInterval timer = DateUtil.timer();
                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                List<Object> nextSearchAfter=null;

                ExcelWriter excelWriter = EasyExcel.write(outputStream, OrderExportListDTO.class).build();
                do {
                    try{
                        SAPageInfo<EsOrdersDTO> saPageInfo = esOrderMapper.searchAfterPage(wa,  nextSearchAfter, 10000);
                        nextSearchAfter = saPageInfo.getNextSearchAfter();
                        if (CollUtil.isNotEmpty(nextSearchAfter)) {
                            List<EsOrdersDTO> pageData = saPageInfo.getList();
                            CopyOptions copyOptions = CopyOptions.create()
                                                                 .setFieldMapping(new HashMap<String, String>() {{
                                        // 在这里添加源字段名到目标字段名的映射，例如:
                                        put("currency", "currencyCode");  // EsOrdersDTO.currency -> OrderExportListDTO.currencyCode
                                        put("productName", "name");
                                        put("channelAlias", "channelName");
                                        put("totalQuantity", "quantity");
                                        put("warehouseSystemCode", "logisticsWarehouse");
                                    }});
                            List<OrderExportListDTO> orderExportListDTOS = BeanUtil.copyToList(pageData, OrderExportListDTO.class, copyOptions);
                            // 处理当前页数据（如存储或业务逻辑）
                            String temporaryTable="order_export"+ System.currentTimeMillis();
                            temporaryTableMapper.createTemporaryTable(temporaryTable);
                            dealOrdersTemporaryTables(orderExportListDTOS,null,temporaryTable);

                            // 查询订单关联地址信息
                            ConcurrentMap<Long, OrderExportListDTO> temporaryOrderAddressAndLogisticsMap = getTemporaryOrderAddressAndLogisticsMap(temporaryTable);
                            // 查询订单关联物流跟踪信息
                            ConcurrentMap<String, OrderExportListDTO> temporaryTrackingRecordMap = getTemporaryTrackingRecordMap(temporaryTable);
                            orderExportListDTOS.forEach(s->{
//                                if(ObjectUtil.isNotEmpty(s.getLatestDeliveryTime())){
//                                    String format = DateUtil.format(DateUtil.parseDate(s.getLatestDeliveryTime()), "yyyy-MM-dd HH:mm:ss");
//                                    s.setLatestDeliveryTime(format);
//                                }
                                s.setErpSku(s.getSku());
                                Warehouse warehouse = warehouseMap.get(s.getLogisticsWarehouse());
                                if (ObjectUtil.isNotNull(warehouse)){
                                    s.setLogisticsWarehouse(warehouse.getWarehouseName());
                                }
                                //地址和物流信息
                                OrderExportListDTO orderAddressAndLogisticsMap = temporaryOrderAddressAndLogisticsMap.get(s.getId());
                                if (orderAddressAndLogisticsMap != null) {
                                    s.setAddress(orderAddressAndLogisticsMap.getAddress());
                                    s.setRecipient(orderAddressAndLogisticsMap.getRecipient());
                                    s.setZipCode(orderAddressAndLogisticsMap.getZipCode());
                                    s.setPhoneNumber(orderAddressAndLogisticsMap.getPhoneNumber());
                                    s.setCity(orderAddressAndLogisticsMap.getCity());
                                    s.setCountry(orderAddressAndLogisticsMap.getCountry());
                                    s.setState(orderAddressAndLogisticsMap.getState());
                                }

                                //物流跟踪单
                                if (s.getOrderNo() != null) {
                                    OrderExportListDTO trackingRecord = temporaryTrackingRecordMap.get(s.getOrderNo());
                                    if (ObjectUtil.isNotNull(trackingRecord)){
                                        s.setLogisticsCarrier(trackingRecord.getLogisticsCarrier());
                                        s.setLogisticsTrackingNo(trackingRecord.getLogisticsTrackingNo());
                                        s.setDispatchedTime(trackingRecord.getDispatchedTime());
                                    }
                                }

                                //处理金额
                                if (Objects.equals(tenantType, TenantType.Supplier.name())) {
                                    //销售额金额：
                                    s.setTotalAmount(s.getOriginalActualTotalAmount());
                                    //如果异常码为空，同时异常码不等于1
                                    if (ObjectUtil.isNull(s.getExceptionCode()) || s.getExceptionCode() != 1) {
                                        //原始应付总金额（供货商）
                                        s.setProductAmount(s.getOriginalPayableTotalAmount());
                                        //单价
                                        s.setProductSkuUnitPrice(String.valueOf(s.getOriginalUnitPrice()));
                                        //原始操作费总金额（供货商）
                                        s.setOperationFee(s.getOriginalTotalOperationFee());
                                        //原始尾程派送费总额
                                        s.setFinalDeliveryFee(s.getOriginalTotalFinalDeliveryFee());
                                        //实付金额：
                                        s.setTotal(s.getOriginalActualTotalAmount());
                                        // 小计
                                        if (ObjectUtil.equal(LogisticsTypeEnum.PickUp.name(),s.getLogisticsType())){
                                            s.setCommoditySubtotal(String.valueOf(s.getOriginalTotalPickUpPrice()));
                                        }else {
                                            s.setCommoditySubtotal(String.valueOf(s.getOriginalTotalDropShippingPrice()));
                                        }
                                    }
                                } else {
                                    //销售额金额：
                                    s.setTotalAmount(s.getPlatformActualTotalAmount());
                                    if (ObjectUtil.isNull(s.getExceptionCode()) || s.getExceptionCode() != 1) {
                                        //实付金额：
                                        s.setTotal(s.getOriginalActualTotalAmount());
                                        //应付金额
                                        //  orderPageVo.setProductAmount(String.valueOf(orders.getOriginalActualTotalAmount()));
                                        s.setProductAmount(s.getPlatformPayableTotalAmount());
                                        //单价
                                        s.setProductSkuUnitPrice(String.valueOf(s.getPlatformUnitPrice()));

                                        //原始操作费总金额（供货商）
                                        s.setOperationFee(s.getPlatformTotalOperationFee());
                                        //原始尾程派送费总额
                                        s.setFinalDeliveryFee(s.getPlatformTotalFinalDeliveryFee());
                                        // 小计
                                        if (ObjectUtil.equal(LogisticsTypeEnum.PickUp.name(),s.getLogisticsType())){
                                            s.setCommoditySubtotal(String.valueOf(s.getOriginalTotalPickUpPrice()));
                                        }else {
                                            s.setCommoditySubtotal(String.valueOf(s.getOriginalTotalDropShippingPrice()));
                                        }
                                    }
                                }
                                // 发货异常
                                if(StringUtils.isNotEmpty(s.getShipmentException())){
                                    switch (s.getShipmentException()){
                                        case "systemException":
                                            s.setShipmentException("系统异常");
                                            break;
                                        case "produceException":
                                            s.setShipmentException("商品无法关联");
                                            break;
                                        case "addressException":
                                            s.setShipmentException("订单地址异常");
                                            break;
                                        case "updateException":
                                            s.setShipmentException("订单更新异常");
                                            break;
                                        case "pickingException":
                                            s.setShipmentException("订单配货异常");
                                            break;
                                        case "cancelException":
                                            s.setShipmentException("订单取消异常");
                                            break;
                                        case "trackingException":
                                            s.setShipmentException("Tracking异常");
                                            break;
                                        case "costException":
                                            s.setShipmentException("订单成本异常");
                                            break;
                                        case "carrierException":
                                            s.setShipmentException("订单发运方式异常");
                                            break;
                                        case "uploadException":
                                            s.setShipmentException("订单附件上传异常");
                                            break;
                                        case "purchaseException":
                                            s.setShipmentException("订单购买异常");
                                            break;
                                        case "insufficientInventoryException":
                                            s.setShipmentException("库存不足异常");
                                            break;
                                        case "deliverOrderCancelException":
                                            s.setShipmentException("仓库取消单据异常");
                                            break;
                                        case "configException":
                                            s.setShipmentException("配置异常");
                                            break;
                                        case "platformPurchaseException":
                                            s.setShipmentException("订单购买异常-平台时间限制");
                                            break;
                                        default:
                                            s.setShipmentException("无异常");
                                    }
                                }else {
                                    s.setShipmentException("无异常");
                                }
                            });
                            WriteSheet writeSheet = EasyExcel.writerSheet(0, "订单导出")
                                                             // 这里放入动态头
                                                             .head(OrderExportListDTO.class)
                                                             //传入样式
                                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                                             .registerConverter(new ExcelBigNumberConvert())
                                                             // 当然这里数据也可以用 List<List<String>> 去传入
                                                             .build();
                            List<String> excludeColumnFieldNames = new ArrayList<>();
                            //不是分销商导出的表格中 隐藏"渠道店铺名" ,"渠道SKU"字段
                            if (Objects.equals(tenantType, TenantType.Distributor.name())) {
                                //分销商
                                excludeColumnFieldNames.add("erpSku");
                                //分销商ID
                                excludeColumnFieldNames.add("tenantId");
                            } else if (ObjectUtil.equal(tenantType,TenantType.Supplier.name())){
                                //供应商
                                //供应商ID
                                excludeColumnFieldNames.add("supplierTenantId");
                                excludeColumnFieldNames.add("channelSku");
                                excludeColumnFieldNames.add("channelName");
                            }else {
                                excludeColumnFieldNames.add("channelSku");
                                excludeColumnFieldNames.add("channelName");
                            }
                            //不是超管导出订单，隐藏支付时间字段
                            //if (!ObjectUtil.equals(tenantType,TenantType.Manager.name())){
                            //    excludeColumnFieldNames.add("createTime");
                            // }
                            writeSheet.setExcludeColumnFieldNames(excludeColumnFieldNames);
                            excelWriter.write(orderExportListDTOS, writeSheet);
                            temporaryTableMapper.dropTemporaryTable(temporaryTable);
                        }

                    }catch (Exception e){
                        log.error(StrUtil.format(StrUtil.format("[订单导出],处理数据异常，原因：", e)));
                        throw new RuntimeException(e);
                    }
                } while (nextSearchAfter != null);
                excelWriter.finish();
                IoUtil.close(outputStream);
                Console.log("[订单导出]耗时: {} ms", timer.intervalMs());
                return tempFile;
            } catch (Exception e) {
                log.error(StrUtil.format(StrUtil.format("[订单导出],处理数据异常，原因：", e)));
                throw new RuntimeException(e);
            }
        });
        return R.ok();
    }

    @Override
    public LambdaEsQueryWrapper<EsOrdersDTO> getEsOrdersQueryWrapper(OrdersPageBo dto) {
        OrdersPageBo bo = getOrdersPageBo(dto);
        LambdaEsQueryWrapper<EsOrdersDTO> q = new LambdaEsQueryWrapper<>();
        q.eq(EsOrdersDTO::getDelFlag,0);
        if (StrUtil.isNotEmpty(bo.getOrderNo())){
            q.likeRight(EsOrdersDTO::getOrderNo,bo.getOrderNo());
        }
        q.in(CollUtil.isNotEmpty(bo.getOrderNos()),EsOrdersDTO::getOrderNo,bo.getOrderNos());
        if (StrUtil.isNotEmpty(bo.getChannelOrderNo())){
            q.likeRight(EsOrdersDTO::getChannelOrderNo,bo.getChannelOrderNo());
        }
        q.in(CollUtil.isNotEmpty(bo.getChannelOrderNos()),EsOrdersDTO::getChannelOrderNo,bo.getChannelOrderNos());
        q.eq(StrUtil.isNotEmpty(bo.getSku()),EsOrdersDTO::getSku,bo.getSku());
        q.match(StrUtil.isNotEmpty(bo.getProductName()),EsOrdersDTO::getProductName,bo.getProductName());
        q.eq(StrUtil.isNotEmpty(bo.getItemNo()),EsOrdersDTO::getProductSkuCode,bo.getItemNo());
        q.eq(StrUtil.isNotEmpty(bo.getTrackingNo()),EsOrdersDTO::getLogisticsTrackingNos,bo.getTrackingNo());
        q.eq(StrUtil.isNotEmpty(bo.getDistributorTenantId()),EsOrdersDTO::getTenantId,bo.getDistributorTenantId());
        q.eq(StrUtil.isNotEmpty(bo.getSupplierTenantId()),EsOrdersDTO::getSupplierTenantId,bo.getSupplierTenantId());
        q.eq(StrUtil.isNotEmpty(bo.getChannelType()),EsOrdersDTO::getChannelType,bo.getChannelType());
        q.eq(StrUtil.isNotEmpty(bo.getLogisticsType()),EsOrdersDTO::getLogisticsType,bo.getLogisticsType());
        q.eq(ObjectUtil.isNotNull(bo.getExceptionCode()),EsOrdersDTO::getExceptionCode,bo.getExceptionCode());
        q.eq(ObjectUtil.isNotNull(bo.getIsShowOrder()),EsOrdersDTO::getIsShow, bo.getIsShowOrder() ?2:1);
        q.eq(ObjectUtil.isNotNull(bo.getOrderSource()),EsOrdersDTO::getOrderSource, bo.getOrderSource());
        q.eq(StrUtil.isNotEmpty(bo.getCurrencyCode()),EsOrdersDTO::getCurrency, bo.getCurrencyCode());
        q.in(CollUtil.isNotEmpty(bo.getOrderStates()),EsOrdersDTO::getOrderState, bo.getOrderStates());
        q.eq(StrUtil.isNotEmpty(bo.getFulfillmentType()),EsOrdersDTO::getFulfillmentProgress, bo.getFulfillmentType());
        q.eq(ObjectUtil.isNotNull(bo.getCancelStatus()),EsOrdersDTO::getCancelStatus, bo.getCancelStatus());

        q.ge(StrUtil.isNotEmpty(bo.getStartDate()),EsOrdersDTO::getCreateTime,bo.getStartDate());
        q.le(StrUtil.isNotEmpty(bo.getEndDate()),EsOrdersDTO::getCreateTime,bo.getEndDate());

        q.ge(StrUtil.isNotEmpty(bo.getLatestDeliveryStartTime()),EsOrdersDTO::getLatestDeliveryTime,bo.getLatestDeliveryStartTime());
        q.le(StrUtil.isNotEmpty(bo.getLatestDeliveryEndTime()),EsOrdersDTO::getLatestDeliveryTime,bo.getLatestDeliveryEndTime());

        q.ge(StrUtil.isNotEmpty(bo.getDispatchedStartTime()),EsOrdersDTO::getDispatchedTime,bo.getDispatchedStartTime());
        q.le(StrUtil.isNotEmpty(bo.getDispatchedEndTime()),EsOrdersDTO::getDispatchedTime,bo.getDispatchedStartTime());
        q.ge(StrUtil.isNotEmpty(bo.getChannelOrderStartDate()),EsOrdersDTO::getChannelOrderTime,bo.getChannelOrderStartDate());
        q.le(StrUtil.isNotEmpty(bo.getChannelOrderEndDate()),EsOrdersDTO::getChannelOrderTime,bo.getChannelOrderEndDate());
        q.eq(StrUtil.isNotEmpty(bo.getShipmentException()),EsOrdersDTO::getShipmentException, bo.getShipmentException());
        q.eq(StrUtil.isNotEmpty(bo.getSupplierTenantId()),EsOrdersDTO::getSupplierTenantId, bo.getSupplierTenantId());
        q.eq(StrUtil.isNotEmpty(bo.getDistributorTenantId()),EsOrdersDTO::getTenantId, bo.getDistributorTenantId());

        if (dto.getIsAssignmentSort() && StrUtil.isNotEmpty(dto.getSortValue())){
            String sortValue = dto.getSortValue();
            switch (sortValue) {
                case "createTimeAsc":
                    q.orderByAsc(EsOrdersDTO::getCreateTime);
                    break;
                case "createTimeDesc":
                    q.orderByDesc(EsOrdersDTO::getCreateTime);
                    break;
                case "orderNoAsc":
                    q.orderByAsc(EsOrdersDTO::getId);
                    break;
                case "orderNoDesc":
                    q.orderByDesc(EsOrdersDTO::getId);
                    break;
                case "priceAsc":
                    q.orderByAsc(EsOrdersDTO::getOriginalActualTotalAmount);
                    break;
                case "priceDesc":
                    q.orderByDesc(EsOrdersDTO::getOriginalActualTotalAmount);
                    break;
                case "latestDeliveryTimeDesc":
                    q.orderByDesc(EsOrdersDTO::getLatestDeliveryTime);
                    break;
                case "latestDeliveryTimeAsc":
                    q.orderByAsc(EsOrdersDTO::getLatestDeliveryTime);
                default:
                    q.orderByDesc(EsOrdersDTO::getId);
                    break;
            }
        }
        return q;
    }

    /**
     * 处理订单附件
     * @param temporaryTableName 临时表
     * @throws IOException
     */
    private void dealOrderAttachmentByEs(String temporaryTableName,String rootPath,String exportPath) throws IOException {

        List<OrderAttachmentExportDto> dtos = ordersMapper.selectOrderAttach(temporaryTableName);
        //获取订单承运商类型
        List<OrderExportListDTO> temporaryTrackingRecord = TenantHelper.ignore(() -> ordersMapper.selectOrderTrackingRecord(temporaryTableName), TenantType.Supplier, TenantType.Manager);
        //处理物流单号
        Map<String, OrderExportListDTO> temporaryTrackingRecordMap = temporaryTrackingRecord.stream().collect(Collectors.toConcurrentMap(
            // key为orderNo
            OrderExportListDTO::getOrderNo,
            // value为OrderExportListDTO对象本身
            Function.identity(),
            (existingValue, newValue) -> {
                // 如果key重复，则拼接logistics，但确保长度不超过200
                if ((existingValue.getLogisticsTrackingNo() + "," + newValue.getLogisticsTrackingNo()).length() <= 210) {
                    existingValue.setLogisticsTrackingNo(existingValue.getLogisticsTrackingNo() + "," + newValue.getLogisticsTrackingNo());
                }
                // 如果拼接后的长度超过200，则不进行拼接，返回现有的existingValue
                return existingValue;
            }
        ));

        //获取文件
        dtos.forEach(s->{
            OrderExportListDTO orderExportListDTO = temporaryTrackingRecordMap.get(s.getOrderNo());
            if (ObjectUtil.isNotNull(orderExportListDTO)){
                s.setLogisticsCarrier(orderExportListDTO.getLogisticsCarrier());
                s.setLogisticsTrackingNo(orderExportListDTO.getLogisticsTrackingNo());
            }
            //文件后缀名
            String extension ="."+ s.getOssUrl().split("\\.")[s.getOssUrl().split("\\.").length - 1];
            String downloadPath=rootPath+"/"+s.getLogisticsCarrier();
            String copyPath=exportPath+"/"+s.getLogisticsCarrier();
            String filePoolName=s.getOrderNo()+"_"+s.getOrderAttachmentId();
            String fileNewName=s.getOrderNo()+"_"+s.getLogisticsTrackingNo();
            //redis中获取文件
            Object cacheMapValue = RedisUtils.getCacheMapValue(GlobalConstants.ORDER_ATTACHMENT,  filePoolName);
            if (ObjectUtil.isNotNull(cacheMapValue)){
                //如果存在，从公共目录里面拷贝到临时目录
                // 检查目标文件是否存在
                Path targetPath = Paths.get(copyPath + "/" + fileNewName + extension);
                // 检查目标文件是否存在，如果存在则重命名
                int counter = 1;
                while (Files.exists(targetPath)) {
                    // 在文件名中加入计数器来生成新的文件名
                    String newFileName = fileNewName + "_" + counter + extension;
                    targetPath = Path.of(copyPath, newFileName);
                    counter++;
}
                FileUtils.copy(Paths.get(downloadPath +"/"+ filePoolName + extension),targetPath, StandardCopyOption.REPLACE_EXISTING);
            }else {
                //判断当前文件是否在文件池中已经存在
                boolean empty = FileUtils.isEmpty(Paths.get(downloadPath +"/"+ filePoolName + extension).toFile());
                if (empty){
                    try {
                        downloadFile(s.getOssUrl(),filePoolName,fileNewName,downloadPath,copyPath,extension);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }else {
                    //上传到redis
                    HashMap<String, String> map = new HashMap<>();
                    map.put(filePoolName,s.getOssUrl());
                    RedisUtils.setCacheMap(GlobalConstants.ORDER_ATTACHMENT,map);
                    //将公共地方的文件拷贝一份到下载目录
                    Path targetPath = Paths.get(copyPath + "/" + fileNewName + extension);
                    // 检查目标文件是否存在，如果存在则重命名
                    int counter = 1;
                    while (Files.exists(targetPath)) {
                        // 在文件名中加入计数器来生成新的文件名
                        String newFileNames = fileNewName + "_" + counter + extension;
                        targetPath = Path.of(copyPath, newFileNames);
                        counter++;
                    }
                    try {
                        fileCopy(Paths.get(downloadPath + "/" + filePoolName + extension), targetPath);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
        log.info(StrUtil.format("下载完成"));
    }

    /**
     *  高效复制文件
     *  当文件小于2MB时，使用 Files.copy() 方法进行复制。
     *  当文件大于2MB时，使用 FileChannel 进行复制，以提高效率。
     * @param source
     * @param target
     * @throws IOException
     */
    public static void fileCopy(Path source, Path target) throws IOException {
        // 获取文件大小
        long size = Files.size(source);

        if (size < 2 * 1024 * 1024) { // 小于2MB的文件
            // 使用 Files.copy
            Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.COPY_ATTRIBUTES);
        } else {
            // 大文件使用 FileChannel
            try (FileChannel sourceChannel = FileChannel.open(source, StandardOpenOption.READ);
                 FileChannel targetChannel = FileChannel.open(target, StandardOpenOption.CREATE,
                     StandardOpenOption.WRITE)) {
                sourceChannel.transferTo(0, sourceChannel.size(), targetChannel);
            }
        }
    }

    @Override
    public void dealOrderDataByInsertEs() {
        try {
            // 获取数据总量
            int rowCount = TenantHelper.ignore(ordersMapper::countOrder);
            if (rowCount != 0) {
                // 一次查询的数据量
                int pageSize = 2000;
                // 总页数
                int totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);

                // 预先查询并缓存所有公共数据，避免每个任务重复查询
//                LambdaQueryWrapper<TenantSalesChannel> qss = new LambdaQueryWrapper<>();
//                List<TenantSalesChannel> tenantSalesChannels = TenantHelper.ignore(() -> tenantSalesChannelService.getBaseMapper().selectList(qss));
//                Map<Long, TenantSalesChannel> tenantSalesChannelHashMap = tenantSalesChannels.stream().collect(Collectors.toMap(TenantSalesChannel::getId, item -> item, (k1, k2) -> k1));

//        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
//        List<Warehouse> warehouses = TenantHelper.ignore(() -> warehouseService.getBaseMapper().selectList(queryWrapper));
//        Map<String, Warehouse> warehouseHashMap = warehouses.stream().collect(Collectors.toMap(Warehouse::getWarehouseSystemCode, item -> item, (k1, k2) -> k1));

                // 创建限制并发度的线程池，限制为5个线程
                ThreadPoolExecutor limitedThreadPool = new ThreadPoolExecutor(
                    2, // 核心线程数
                    2, // 最大线程数
                    60L,
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(), // 使用无界队列
                    Executors.defaultThreadFactory(),
                    new ThreadPoolExecutor.CallerRunsPolicy() // 当队列满时，由调用线程执行
                );

                // 使用CountDownLatch来等待所有任务完成
                CountDownLatch latch = new CountDownLatch(totalPage);

                // 创建异常收集器
                ConcurrentLinkedQueue<Exception> exceptions = new ConcurrentLinkedQueue<>();

                log.info("开始分页处理订单ES数据，总数据量: {}, 总页数: {}, 每页数据量: {}", rowCount, totalPage, pageSize);

                for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
                    final int page = currentPage;
                    // 计算当前页的起始行
                    final int startRow = (page - 1) * pageSize;
                    // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
                    final int finalPageSize = (page == totalPage && rowCount % pageSize != 0) ? (rowCount % pageSize) : pageSize;

                    // 使用限制并发的线程池
                    limitedThreadPool.execute(() -> {
                        // 使用更具唯一性的临时表名
                        String temporaryTableName = "OrderDataByInsertEs_" + UUID.randomUUID().toString().replace("-", "");
                        boolean tableCreated = false;
                        try {
                            log.info("开始处理第 {} 页订单ES数据，数据范围: [{} - {}]", page, startRow, startRow + finalPageSize - 1);

                            // 查询订单数据
                            List<EsOrdersDTO> ordersPage = TenantHelper.ignore(() -> ordersMapper.getOrders(startRow, finalPageSize,null));
                            if (CollUtil.isEmpty(ordersPage)) {
                                log.info("第 {} 页没有查询到数据，跳过处理", page);
                                return;
                            }

                            // 创建临时表并标记
                            TenantHelper.ignore(() -> temporaryTableMapper.createTemporaryTable(temporaryTableName));
                            tableCreated = true;

                            // 构建SQL插入临时表
                            StringBuilder sql = new StringBuilder("INSERT INTO " + temporaryTableName + " (value3) VALUES ");
                            for (EsOrdersDTO s : ordersPage) {
                                sql.append(String.format("('%s'),", s.getOrderNo()));
                            }

                            // 移除最后一个逗号并执行插入
                            if (!ordersPage.isEmpty()) {
                                sql.deleteCharAt(sql.length() - 1);
                                // 插入临时表
                                TenantHelper.ignore(() -> temporaryTableMapper.insertBySql(sql.toString()));
                            }

                            // 获取物流跟踪信息和物流信息
                            Map<String, List<String>> orderItemTrackingRecordHashMap = new HashMap<>();
                            Map<String, OrderLogisticsInfo> orderLogisticsInfoHashMap = new HashMap<>();

                            // 处理订单相关数据
                            Set<String> orderNoSet = ordersPage.stream()
                                                               .map(EsOrdersDTO::getOrderNo)
                                                               .filter(StrUtil::isNotEmpty)
                                                               .collect(Collectors.toSet());

                            if (CollUtil.isNotEmpty(orderNoSet)) {
                                try {
                                    List<OrderItemTrackingRecord> orderItemTrackingRecords = TenantHelper.ignore(() ->
                                        ordersMapper.selectOrderItemTrackingRecord(temporaryTableName));

                                    if (CollUtil.isNotEmpty(orderItemTrackingRecords)) {
                                        orderItemTrackingRecordHashMap = orderItemTrackingRecords.stream()
                                                                                                 .filter(record -> StringUtils.isNotEmpty(record.getLogisticsTrackingNo()))
                                                                                                 .collect(Collectors.groupingBy(OrderItemTrackingRecord::getOrderNo,
                                                                                                     Collectors.mapping(OrderItemTrackingRecord::getLogisticsTrackingNo, Collectors.toList())));
                                    }

                                    List<OrderLogisticsInfo> orderLogisticsInfos = TenantHelper.ignore(() ->
                                        ordersMapper.selectOrderLogisticsInfoByEsInsert(temporaryTableName));

                                    if (CollUtil.isNotEmpty(orderLogisticsInfos)) {
                                        orderLogisticsInfoHashMap = orderLogisticsInfos.stream()
                                                                                       .collect(Collectors.toMap(OrderLogisticsInfo::getOrderNo, item -> item, (k1, k2) -> k1));
                                    }
                                } catch (Exception e) {
                                    log.error("处理第 {} 页订单物流信息时发生异常: {}", page, e.getMessage());
                                    // 继续处理，不中断整个流程
                                }
                            }

                            // 处理订单数据
                            List<EsOrdersDTO> list = new ArrayList<>();

                            // 处理每个订单
                            for (EsOrdersDTO esOrdersDTO : ordersPage) {
                                try {
                                    esOrdersDTO.setIsShow(ObjectUtil.isNull(esOrdersDTO.getIsShow()) ? 1 : esOrdersDTO.getIsShow());

                                    // 设置仓库信息
//                  Warehouse warehouse = warehouseHashMap.get(esOrdersDTO.getWarehouseSystemCode());
//                  if (ObjectUtil.isNotNull(warehouse)) {
//                    esOrdersDTO.setWarehouseName(warehouse.getWarehouseName());
//                  }

                                    // 设置物流跟踪信息
                                    if (CollUtil.isNotEmpty(orderItemTrackingRecordHashMap)) {
                                        List<String> logisticsTrackingNos = orderItemTrackingRecordHashMap.get(esOrdersDTO.getOrderNo());
                                        if (CollUtil.isNotEmpty(logisticsTrackingNos)) {
                                            esOrdersDTO.setLogisticsTrackingNos(logisticsTrackingNos);
                                        }
                                    }

                                    // 设置物流信息
                                    if (CollUtil.isNotEmpty(orderLogisticsInfoHashMap)) {
                                        OrderLogisticsInfo orderLogisticsInfo = orderLogisticsInfoHashMap.get(esOrdersDTO.getOrderNo());
                                        if (ObjectUtil.isNotNull(orderLogisticsInfo)) {
                                            esOrdersDTO.setLogisticsAccount(orderLogisticsInfo.getLogisticsAccount());
                                            esOrdersDTO.setLogisticsAccountZipCode(orderLogisticsInfo.getLogisticsAccountZipCode());
                                        }
                                    }

                                    // 设置渠道信息
//                  if (ObjectUtil.isNotEmpty(esOrdersDTO.getChannelId())) {
//                    TenantSalesChannel tenantSalesChannel = tenantSalesChannelHashMap.get(esOrdersDTO.getChannelId());
//                    if (ObjectUtil.isNotNull(tenantSalesChannel)) {
//                      esOrdersDTO.setChannelName(tenantSalesChannel.getChannelName());
//                      esOrdersDTO.setChannelType(tenantSalesChannel.getChannelType());
//                    }
//                  } else {
//                    esOrdersDTO.setChannelName(esOrdersDTO.getChannelAlias());
//                    esOrdersDTO.setChannelType(String.valueOf(esOrdersDTO.getChannelType()));
//                  }
                                    // canalOrderSupper.setOrderNoToRedis(esOrdersDTO.getOrderNo());


                                    list.add(esOrdersDTO);
                                } catch (Exception e) {
                                    log.error("处理订单 {} 数据时发生异常: {}", esOrdersDTO.getOrderNo(), e.getMessage());
                                }
                            }

                            // 批量插入到ES
                            if (!list.isEmpty()) {
                                try {
                                    esOrderMapper.insertBatch(list);
                                    log.info("第 {} 页订单ES数据处理完成，成功导入 {} 条记录", page, list.size());
                                } catch (Exception e) {
                                    log.error("第 {} 页订单ES批量插入时发生异常: {}", page, e.getMessage());
                                    exceptions.add(e);
                                }finally {
                                    // 清理资源
                                    list.clear();
                                    ordersPage.clear();
                                    orderItemTrackingRecordHashMap.clear();
                                    orderLogisticsInfoHashMap.clear();
                                    // 主动提示GC回收
                                    System.gc();
                                }
                            } else {
                                log.warn("第 {} 页没有有效的订单数据可导入ES", page);
                            }
                        } catch (Exception e) {
                            log.error("处理第 {} 页订单ES数据时发生异常: {}", page, e.getMessage(), e);
                            exceptions.add(e);
                        } finally {
                            // 删除临时表
                            if (tableCreated) {
                                try {
                                    TenantHelper.ignore(() -> temporaryTableMapper.dropTemporaryTable(temporaryTableName));
                                } catch (Exception e) {
                                    log.error("删除临时表 {} 时发生异常: {}", temporaryTableName, e.getMessage());
                                }
                            }
                            // 减少计数器
                            latch.countDown();
                        }
                    });
                }

                // 等待所有任务完成
                try {
                    boolean completed = latch.await(60, TimeUnit.MINUTES);
                    if (!completed) {
                        log.error("订单ES数据处理超时");
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("等待订单ES数据处理完成时被中断", e);
                } finally {
                    // 关闭线程池
                    limitedThreadPool.shutdown();
                    try {
                        if (!limitedThreadPool.awaitTermination(10, TimeUnit.SECONDS)) {
                            limitedThreadPool.shutdownNow();
                        }
                    } catch (InterruptedException e) {
                        limitedThreadPool.shutdownNow();
                        Thread.currentThread().interrupt();
                    }
                }
                log.error("订单ES数据处理过程中发生异常，总计{}个异常", exceptions.size());
            }
            log.info("订单ES数据处理任务完成");
        } catch (Exception e) {
            log.error(StrUtil.format("订单ES数据处理异常，原因：{}", e.getMessage()), e);
            throw new RuntimeException(e);
        }
    }



    @Override
    public List<OrderListVo> dealOrderEsSearchListData(List<EsOrdersDTO> list) {
        LambdaQueryWrapper<SysUser> qq = new LambdaQueryWrapper<>();
        List<SysUserVo> sysUserList = TenantHelper.ignore(() -> sysUserMapper.selectUserList(qq));
        Map<Long, SysUserVo> userMap = sysUserList.stream()
                                                  .collect(Collectors.toMap(SysUserVo::getUserId, sysUser -> sysUser));

        List<OrderListVo> orderList = list.stream()
                                        .map(s -> {
                                            OrderListVo vo = new OrderListVo();
                                            BeanUtil.copyProperties(s, vo);
                                            vo.setChannelOrderId(s.getChannelOrderNo());
                                            vo.setOrderId(s.getOrderNo());
                                            // 添加特殊字段处理（可选）
                                            return vo;
                                        })
                                        .collect(Collectors.toList());

        List<String> exceptionOrderSns = orderList.stream()
                                                     .filter(ObjectUtil::isNotEmpty)
                                                     .filter(order ->
                                                         OrderExceptionEnum.abnormal_exception.getValue()
                                                                                              .equals(order.getExceptionCode()))
                                                     .map(OrderListVo::getOrderId)
                                                     .collect(Collectors.toList());

        Map<String, JSONObject> map = null;
        if(CollUtil.isNotEmpty(exceptionOrderSns)){
            map = iOrderItemShippingRecordService.queryMapByOrderNoList(exceptionOrderSns);
        }

        //转换成OrderListVo
        List<OrderListVo> orderListVos=new ArrayList<>();
        for (EsOrdersDTO s : list) {
            OrderListVo ol = new OrderListVo();
            BeanUtil.copyProperties(s, ol);
            ol.setChannelOrderId(s.getChannelOrderNo());
            ol.setOrderId(s.getOrderNo());
            ol.setPayFailedMessage(JSONUtil.parseObj(s.getPayErrorMessage()));
            String orderId = ol.getOrderId();

            if(CollUtil.isNotEmpty(map)&&map.containsKey(orderId)){
                ol.setAbnormalMessage(map.get(orderId));
            }

            if (ObjectUtil.isNotNull(s.getOrderState())) {
                List<String> orderStatusTypes = new ArrayList<>();
                orderStatusTypes.add(OrderStateType.UnPaid.name());
                orderStatusTypes.add(OrderStateType.Failed.name());
                orderStatusTypes.add(OrderStateType.Canceled.name());
                orderStatusTypes.add(OrderStateType.Refunded.name());
                orderStatusTypes.add(OrderStateType.Pending.name());
                if (orderStatusTypes.contains(s.getOrderState())) {
                    ol.setOrderStatus(s.getOrderState());
                }else {
                    ol.setOrderStatus(s.getFulfillmentProgress());
                }
            }
            ol.setSalesChannel(s.getChannelType());
            String tenantType = LoginHelper.getTenantType();
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal productTotalPrice = BigDecimal.ZERO;
            //处理金额
            if (ObjectUtil.equals(tenantType, TenantType.Supplier.name())) {
                productTotalPrice = s.getOriginalActualTotalAmount();
                totalAmount = productTotalPrice;
            } else {
                if(OrderSourceEnum.INTERFACE_ORDER.getValue().equals(s.getOrderSource()) ||
                    OrderSourceEnum.OPEN_API_ORDER.getValue().equals(s.getOrderSource())) {
                    if(LogisticsTypeEnum.DropShipping.name().equals(s.getLogisticsType())){
                        productTotalPrice = s.getOriginalTotalDropShippingPrice();
                    }
                    if(LogisticsTypeEnum.PickUp.name().equals(s.getLogisticsType())){
                        productTotalPrice = s.getOriginalTotalPickUpPrice();
                    }
                } else {
                    productTotalPrice = s.getPlatformActualTotalAmount();
                }

                if(LogisticsTypeEnum.DropShipping.name().equals(s.getLogisticsType())){
                    totalAmount = s.getOriginalTotalDropShippingPrice();
                }
                if(LogisticsTypeEnum.PickUp.name().equals(s.getLogisticsType())){
                    totalAmount = s.getOriginalTotalPickUpPrice();
                }
            }
            SysUserVo sysUser = userMap.get(s.getCreateBy());
            if (ObjectUtil.isNotNull(sysUser)){
                ol.setDistributor( DesensitizedUtil.idCardNum(sysUser.getUserName(), 1, 1));
            }
            ol.setDistributorId(s.getTenantId());
            ol.setSupplierIds(s.getSupplierTenantId());
            //处理超管分销商/供应商信息
            if (!ObjectUtil.equals(TenantType.Manager.name(), tenantType)) {
                ol.setDistributorId(null);
                ol.setSupplierIds(null);
                ol.setDistributor(null);
            }
            ol.setTotal(totalAmount);
            OrderListVo.OrderItems orderItem = new OrderListVo.OrderItems();
            BeanUtil.copyProperties(s,orderItem);
            orderItem.setProductTotalPrice(productTotalPrice);
            orderItem.setItemNo(s.getProductSkuCode());
            orderItem.setNum(s.getTotalQuantity());
//            s.setTotal(ObjectUtil.isNotNull(totalAmount)?totalAmount:BigDecimal.ZERO);
//            orderItems.setProductTotalPrice(ObjectUtil.isNotNull(productTotalPrice)?productTotalPrice:BigDecimal.ZERO);
            String orderItemStatus = ol.getOrderState();
            String fulfillment = s.getFulfillmentProgress();
            //子订单确认收货按钮
            boolean canConfirmReceipt = ObjectUtil.equals(orderItemStatus, OrderStateType.Paid) && ObjectUtil.equals(fulfillment, LogisticsProgress.Dispatched);
            orderItem.setCanConfirmReceipt(canConfirmReceipt);
            ol.setOrderItems(List.of(orderItem));
            orderListVos.add(ol);
        }
        return orderListVos;
    }


    @Override
    public List<EsOrdersDTO> getEsOrderDateByOrderNo(Set<String> orderNos) {
//        LambdaQueryWrapper<TenantSalesChannel> qss = new LambdaQueryWrapper<>();
//        List<TenantSalesChannel> tenantSalesChannels = TenantHelper.ignore(() -> tenantSalesChannelService.getBaseMapper().selectList(qss));
//        Map<Long, TenantSalesChannel> tenantSalesChannelHashMap = tenantSalesChannels.stream().collect(Collectors.toMap(TenantSalesChannel::getId, item -> item, (k1, k2) -> k1));

//            LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
//            List<Warehouse> warehouses = TenantHelper.ignore(() -> warehouseService.getBaseMapper().selectList(queryWrapper));
//            Map<String, Warehouse> warehouseHashMap = warehouses.stream().collect(Collectors.toMap(Warehouse::getWarehouseSystemCode, item -> item, (k1, k2) -> k1));
        // 处理订单数据
        List<EsOrdersDTO> list = new ArrayList<>();
        List<EsOrdersDTO> ordersPage = TenantHelper.ignore(() -> ordersMapper.getOrders(0, 0,orderNos));
        if (CollUtil.isNotEmpty(ordersPage)){
            String temporaryTableName = "OrderDataByInsertEs_" + UUID.randomUUID().toString().replace("-", "");
            TenantHelper.ignore(() -> temporaryTableMapper.createTemporaryTable(temporaryTableName));
            // 构建SQL插入临时表
            StringBuilder sql = new StringBuilder("INSERT INTO " + temporaryTableName + " (value3) VALUES ");
            for (EsOrdersDTO s : ordersPage) {
                sql.append(String.format("('%s'),", s.getOrderNo()));
            }
            // 移除最后一个逗号并执行插入
            if (!ordersPage.isEmpty()) {
                sql.deleteCharAt(sql.length() - 1);
                // 插入临时表
                TenantHelper.ignore(() -> temporaryTableMapper.insertBySql(sql.toString()));
            }

            // 获取物流跟踪信息和物流信息
            Map<String, List<String>> orderItemTrackingRecordHashMap = new HashMap<>();
            Map<String, OrderLogisticsInfo> orderLogisticsInfoHashMap = new HashMap<>();
            List<OrderItemTrackingRecord> orderItemTrackingRecords = TenantHelper.ignore(() ->
                ordersMapper.selectOrderItemTrackingRecord(temporaryTableName));

            if (CollUtil.isNotEmpty(orderItemTrackingRecords)) {
                orderItemTrackingRecordHashMap = orderItemTrackingRecords.stream()
                                                                         .filter(record -> StringUtils.isNotEmpty(record.getLogisticsTrackingNo()))
                                                                         .collect(Collectors.groupingBy(OrderItemTrackingRecord::getOrderNo,
                                                                             Collectors.mapping(OrderItemTrackingRecord::getLogisticsTrackingNo, Collectors.toList())));
            }

            List<OrderLogisticsInfo> orderLogisticsInfos = TenantHelper.ignore(() ->
                ordersMapper.selectOrderLogisticsInfoByEsInsert(temporaryTableName));

            if (CollUtil.isNotEmpty(orderLogisticsInfos)) {
                orderLogisticsInfoHashMap = orderLogisticsInfos.stream()
                                                               .collect(Collectors.toMap(OrderLogisticsInfo::getOrderNo, item -> item, (k1, k2) -> k1));
            }

            // 处理每个订单
            for (EsOrdersDTO esOrdersDTO : ordersPage) {
                try {
                    esOrdersDTO.setIsShow(ObjectUtil.isNull(esOrdersDTO.getIsShow()) ? 1 : esOrdersDTO.getIsShow());

//                    // 设置仓库信息
//                    Warehouse warehouse = warehouseHashMap.get(esOrdersDTO.getWarehouseSystemCode());
//                    if (ObjectUtil.isNotNull(warehouse)) {
//                        esOrdersDTO.setWarehouseName(warehouse.getWarehouseName());
//                    }

                    // 设置物流跟踪信息
                    if (CollUtil.isNotEmpty(orderItemTrackingRecordHashMap)) {
                        List<String> logisticsTrackingNos = orderItemTrackingRecordHashMap.get(esOrdersDTO.getOrderNo());
                        if (CollUtil.isNotEmpty(logisticsTrackingNos)) {
                            esOrdersDTO.setLogisticsTrackingNos(logisticsTrackingNos);
                        }
                    }

                    // 设置物流信息
                    if (CollUtil.isNotEmpty(orderLogisticsInfoHashMap)) {
                        OrderLogisticsInfo orderLogisticsInfo = orderLogisticsInfoHashMap.get(esOrdersDTO.getOrderNo());
                        if (ObjectUtil.isNotNull(orderLogisticsInfo)) {
                            esOrdersDTO.setLogisticsAccount(orderLogisticsInfo.getLogisticsAccount());
                            esOrdersDTO.setLogisticsAccountZipCode(orderLogisticsInfo.getLogisticsAccountZipCode());
                        }
                    }

                    // 设置渠道信息
//                    if (ObjectUtil.isNotEmpty(esOrdersDTO.getChannelId())) {
//                        TenantSalesChannel tenantSalesChannel = tenantSalesChannelHashMap.get(esOrdersDTO.getChannelId());
//                        if (ObjectUtil.isNotNull(tenantSalesChannel)) {
//                            esOrdersDTO.setChannelName(tenantSalesChannel.getChannelName());
//                            esOrdersDTO.setChannelType(tenantSalesChannel.getChannelType());
//                        }
//                    } else {
//                        esOrdersDTO.setChannelName(esOrdersDTO.getChannelAlias());
//                        esOrdersDTO.setChannelType(esOrdersDTO.getChannelType());
//                    }

                    list.add(esOrdersDTO);
                } catch (Exception e) {
                    log.error("处理订单 {} 数据时发生异常: {}", esOrdersDTO.getOrderNo(), e.getMessage());
                }
            }
            TenantHelper.ignore(() -> temporaryTableMapper.dropTemporaryTable(temporaryTableName));
        }
        return list;
    }

    @Override
    public void checkEsOrderDate() {
        Long esCount = esOrderMapper.selectCount(new LambdaEsQueryWrapper<>());
        int  dbCount = TenantHelper.ignore(ordersMapper::countOrder);
        if (ObjectUtil.notEqual(esCount,dbCount)){
            throw new RuntimeException(StrUtil.format("数据行数不一致，DB数据行数:{},ES数据行数:{}",dbCount,esCount));
        }
    }

    @Override
    public void dealOrderDataByInsertEsWithoutThreads() {
        try {
            // 获取数据总量
            int rowCount = TenantHelper.ignore(ordersMapper::countOrder);
            if (rowCount != 0) {
                // 一次查询的数据量
                int pageSize = 5000;
                // 总页数
                int totalPage = rowCount % pageSize == 0 ? rowCount / pageSize : (rowCount / pageSize + 1);

                log.info("开始分页处理订单ES数据（单线程），总数据量: {}, 总页数: {}, 每页数据量: {}", rowCount, totalPage, pageSize);

                // 单线程顺序处理每一页数据
                for (int currentPage = 1; currentPage <= totalPage; currentPage++) {
                    // 计算当前页的起始行
                    final int startRow = (currentPage - 1) * pageSize;
                    // 如果是最后一页且不足pageSize条数据，调整pageSize为剩余的数据量
                    final int finalPageSize = (currentPage == totalPage && rowCount % pageSize != 0) ? (rowCount % pageSize) : pageSize;

                    // 使用更具唯一性的临时表名
                    String temporaryTableName = "OrderDataByInsertEs_" + UUID.randomUUID().toString().replace("-", "");
                    boolean tableCreated = false;

                    try {
                        log.info("开始处理第 {}/{} 页订单ES数据，数据范围: [{} - {}]", currentPage,totalPage, startRow, startRow + finalPageSize - 1);

                        // 查询订单数据
                        List<EsOrdersDTO> ordersPage = TenantHelper.ignore(() -> ordersMapper.getOrders(startRow, finalPageSize, null));
                        if (CollUtil.isEmpty(ordersPage)) {
                            log.info("第 {}/{} 页没有查询到数据，跳过处理", currentPage,totalPage);
                            continue;
                        }

                        // 创建临时表并标记
                        TenantHelper.ignore(() -> temporaryTableMapper.createTemporaryTable(temporaryTableName));
                        tableCreated = true;

                        // 构建SQL插入临时表
                        StringBuilder sql = new StringBuilder("INSERT INTO " + temporaryTableName + " (value3) VALUES ");
                        for (EsOrdersDTO s : ordersPage) {
                            sql.append(String.format("('%s'),", s.getOrderNo()));
                        }

                        // 移除最后一个逗号并执行插入
                        if (!ordersPage.isEmpty()) {
                            sql.deleteCharAt(sql.length() - 1);
                            // 插入临时表
                            TenantHelper.ignore(() -> temporaryTableMapper.insertBySql(sql.toString()));
                        }

                        // 获取物流跟踪信息和物流信息
                        Map<String, List<String>> orderItemTrackingRecordHashMap = new HashMap<>();
                        Map<String, OrderLogisticsInfo> orderLogisticsInfoHashMap = new HashMap<>();

                        // 处理订单相关数据
                        Set<String> orderNoSet = ordersPage.stream()
                                                           .map(EsOrdersDTO::getOrderNo)
                                                           .filter(StrUtil::isNotEmpty)
                                                           .collect(Collectors.toSet());

                        if (CollUtil.isNotEmpty(orderNoSet)) {
                            try {
                                List<OrderItemTrackingRecord> orderItemTrackingRecords = TenantHelper.ignore(() ->
                                    ordersMapper.selectOrderItemTrackingRecord(temporaryTableName));

                                if (CollUtil.isNotEmpty(orderItemTrackingRecords)) {
                                    orderItemTrackingRecordHashMap = orderItemTrackingRecords.stream()
                                                                                             .filter(record -> StringUtils.isNotEmpty(record.getLogisticsTrackingNo()))
                                                                                             .collect(Collectors.groupingBy(OrderItemTrackingRecord::getOrderNo,
                                                                                                 Collectors.mapping(OrderItemTrackingRecord::getLogisticsTrackingNo, Collectors.toList())));
                                }

                                List<OrderLogisticsInfo> orderLogisticsInfos = TenantHelper.ignore(() ->
                                    ordersMapper.selectOrderLogisticsInfoByEsInsert(temporaryTableName));

                                if (CollUtil.isNotEmpty(orderLogisticsInfos)) {
                                    orderLogisticsInfoHashMap = orderLogisticsInfos.stream()
                                                                                   .collect(Collectors.toMap(OrderLogisticsInfo::getOrderNo, item -> item, (k1, k2) -> k1));
                                }
                            } catch (Exception e) {
                                log.error("处理第 {} 页订单物流信息时发生异常: {}", currentPage, e.getMessage());
                                // 继续处理，不中断整个流程
                            }
                        }

                        // 处理订单数据
                        List<EsOrdersDTO> list = new ArrayList<>();

                        // 处理每个订单
                        for (EsOrdersDTO esOrdersDTO : ordersPage) {
                            try {
                                esOrdersDTO.setIsShow(ObjectUtil.isNull(esOrdersDTO.getIsShow()) ? 1 : esOrdersDTO.getIsShow());

                                // 设置物流跟踪信息
                                if (CollUtil.isNotEmpty(orderItemTrackingRecordHashMap)) {
                                    List<String> logisticsTrackingNos = orderItemTrackingRecordHashMap.get(esOrdersDTO.getOrderNo());
                                    if (CollUtil.isNotEmpty(logisticsTrackingNos)) {
                                        esOrdersDTO.setLogisticsTrackingNos(logisticsTrackingNos);
                                    }
                                }

                                // 设置物流信息
                                if (CollUtil.isNotEmpty(orderLogisticsInfoHashMap)) {
                                    OrderLogisticsInfo orderLogisticsInfo = orderLogisticsInfoHashMap.get(esOrdersDTO.getOrderNo());
                                    if (ObjectUtil.isNotNull(orderLogisticsInfo)) {
                                        esOrdersDTO.setLogisticsAccount(orderLogisticsInfo.getLogisticsAccount());
                                        esOrdersDTO.setLogisticsAccountZipCode(orderLogisticsInfo.getLogisticsAccountZipCode());
                                    }
                                }

                                list.add(esOrdersDTO);
                            } catch (Exception e) {
                                log.error("处理订单 {} 数据时发生异常: {}", esOrdersDTO.getOrderNo(), e.getMessage());
                            }
                        }

                        // 批量插入到ES
                        if (!list.isEmpty()) {
                            try {
                                esOrderMapper.insertBatch(list);
                                log.info("第 {}/{} 页订单ES数据处理完成，成功导入 {} 条记录", currentPage,totalPage, list.size());
                            } catch (Exception e) {
                                log.error("第 {}/{} 页订单ES批量插入时发生异常: {}", currentPage,totalPage, e.getMessage());
                                throw e; // 单线程模式下抛出异常，让外层处理
                            } finally {
                                // 清理资源
                                list.clear();
                                ordersPage.clear();
                                orderItemTrackingRecordHashMap.clear();
                                orderLogisticsInfoHashMap.clear();
                                // 主动提示GC回收
                                System.gc();
                            }
                        } else {
                            log.warn("第 {}/{} 页没有有效的订单数据可导入ES", currentPage,totalPage);
                        }
                    } catch (Exception e) {
                        log.error("处理第 {}/{} 页订单ES数据时发生异常: {}", currentPage,totalPage, e.getMessage(), e);
                        // 不抛出异常，继续处理下一页
                    } finally {
                        // 删除临时表
                        if (tableCreated) {
                            try {
                                TenantHelper.ignore(() -> temporaryTableMapper.dropTemporaryTable(temporaryTableName));
                            } catch (Exception e) {
                                log.error("删除临时表 {} 时发生异常: {}", temporaryTableName, e.getMessage());
                            }
                        }
                    }
                }
                log.info("订单ES数据处理完成（单线程），共处理 {} 页数据", totalPage);
            } else {
                log.info("没有订单数据需要处理");
            }
        } catch (Exception e) {
            log.error("处理订单ES数据时发生异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void insertOrderDateToEsByOrderNos(String orderNos) {
        String[] split = orderNos.split(",");
        List<EsOrdersDTO> esOrderDateByOrderNo = getEsOrderDateByOrderNo(Set.of(split));
        esOrderMapper.insertBatch(esOrderDateByOrderNo);
    }
}




